# 任务中心接口设计文档

## 1. 文档概述

### 1.1 目的
本文档旨在定义任务中心系统的API接口规范，为前后端开发人员提供明确的接口约定，确保系统各模块之间的顺利集成。

### 1.2 适用范围
本文档适用于任务中心系统的前端开发人员、后端开发人员、测试人员以及系统维护人员。

### 1.3 系统架构
任务中心系统采用前后端分离架构：
- 前端：Vue3
- 后端：JDK17
- 持久层框架：MyBatis-Plus
- 数据库：PostgreSQL 14.7
- 数据库连接池：Druid

### 1.4 接口版本控制
所有接口均使用版本号进行管理，当前版本为v1。接口路径格式为：`/api/pc/v1/{resource}`。

## 2. 接口通用规范

### 2.1 请求规范

#### 2.1.1 请求方法
根据接口命名规范，本系统仅使用以下HTTP方法：
- GET：用于获取资源，参数少于等于5个时使用
- POST：用于创建资源、更新资源、删除资源，以及参数超过5个的查询请求

#### 2.1.2 请求路径
接口路径遵循以下格式：
- `/api/pc/v1/{resources}/{resource_id}/{action}`
- 资源名称使用复数形式
- 使用特定动作词表示操作：create(创建)、update(更新)、delete(删除)、query(查询)、page(分页查询)

#### 2.1.3 请求参数
- GET请求：参数通过URL查询字符串传递
- POST请求：参数通过请求体以JSON格式传递，Content-Type设置为application/json

#### 2.1.4 分页参数
分页查询统一使用以下参数：
- pageNum：当前页码，从1开始
- pageSize：每页记录数
- sort：排序字段，格式为"字段名,asc|desc"，多个排序字段用逗号分隔

### 2.2 响应规范

#### 2.2.1 响应格式
所有接口响应均采用JSON格式，基本结构如下：
```json
{
  "code": 0,       // 响应码，0表示成功，非0表示失败
  "msg": "",       // 响应消息，成功时为空或成功提示，失败时为错误提示
  "data": {}       // 响应数据，可以是对象、数组或基本类型
}
```

#### 2.2.2 分页响应格式
分页查询的响应格式如下：
```json
{
  "code": 0,
  "msg": "",
  "data": {
    "data": [],            // 当前页数据列表
    "pageNum": 1,          // 当前页码
    "pageSize": 10,        // 每页记录数
    "totalCount": 100,     // 总记录数
    "totalPage": 10        // 总页数
  }
}
```

#### 2.2.3 响应状态码
- 200：请求成功
- 400：请求参数错误
- 401：未授权
- 403：禁止访问
- 404：资源不存在
- 500：服务器内部错误

## 3. 认证与授权

### 3.1 认证方式
系统采用基于Token的认证方式，所有接口（除登录接口外）都需要在请求头中携带Token进行认证。

### 3.2 请求头设置
```
Authorization: Bearer {token}
```

### 3.3 权限控制
系统根据用户角色进行权限控制，主要分为三级：
1. 管理员：可创建任务、分配任务、查看所有任务
2. 组织机构负责人：可接收任务、指派任务、查看本组织任务
3. 组织机构成员：可接收任务、填报任务、查看自己的任务

## 4. 任务管理接口

### 4.1 任务创建接口

#### 4.1.1 创建任务

- **接口说明**：管理员创建新任务并分配给组织机构负责人
- **请求方法**：POST
- **请求路径**：`/api/pc/v1/tasks/create`
- **请求参数**：

```json
{
  "taskClass": "计划上报",                  // 任务大类，固定为"计划上报"
  "subTaskClass": "月度采气计划",           // 任务小类，"月度采气计划"或"月度注气计划"
  "taskName": "2025年2月采气计划",         // 任务名称
  "planYearMonth": "2025-02",             // 计划年月
  "startTime": "2025-01-15 00:00:00",     // 开始日期
  "endTime": "2025-01-25 23:59:59",       // 结束日期
  "taskDesc": "请各单位按时填报2月采气计划",  // 任务说明
  "assignments": [                         // 任务分配信息
    {
      "orgId": "ORG001",                  // 组织机构ID
      "managerId": "USER001"              // 负责人ID
    },
    {
      "orgId": "ORG002",                  // 组织机构ID
      "managerId": "USER002"              // 负责人ID
    }
  ]
}
```

- **响应参数**：

```json
{
  "code": 0,
  "msg": "创建成功",
  "data": {
    "taskId": "TASK20250115001",           // 任务ID
    "createTime": "2025-01-15 10:30:00"    // 创建时间
  }
}
```

#### 4.1.2 修改任务

- **接口说明**：管理员修改已创建的任务
- **请求方法**：POST
- **请求路径**：`/api/pc/v1/tasks/{taskId}/update`
- **请求参数**：

```json
{
  "taskName": "2025年2月采气计划(修改)",    // 任务名称
  "planYearMonth": "2025-02",             // 计划年月
  "startTime": "2025-01-16 00:00:00",     // 开始日期
  "endTime": "2025-01-26 23:59:59",       // 结束日期
  "taskDesc": "请各单位按时填报2月采气计划"   // 任务说明
}
```

- **响应参数**：

```json
{
  "code": 0,
  "msg": "修改成功",
  "data": {
    "taskId": "TASK20250115001",           // 任务ID
    "updateTime": "2025-01-15 11:30:00"    // 更新时间
  }
}
```

#### 4.1.3 删除任务

- **接口说明**：管理员删除任务
- **请求方法**：POST
- **请求路径**：`/api/pc/v1/tasks/{taskId}/delete`
- **请求参数**：无
- **响应参数**：

```json
{
  "code": 0,
  "msg": "删除成功",
  "data": null
}
```

### 4.2 任务指派接口

#### 4.2.1 指派任务

- **接口说明**：组织机构负责人将任务指派给成员
- **请求方法**：POST
- **请求路径**：`/api/pc/v1/tasks/assignments/assign`
- **请求参数**：

```json
{
  "taskId": "TASK20250115001",             // 任务ID
  "taskAssignId": "ASSIGN20250115001",     // 任务分配ID
  "employeeId": "USER003",                // 指派的成员ID
  "remarks": "请于1月20日前完成填报"         // 备注说明
}
```

- **响应参数**：

```json
{
  "code": 0,
  "msg": "指派成功",
  "data": {
    "taskAssignId": "ASSIGN20250115001",    // 任务分配ID
    "assignTime": "2025-01-16 09:30:00"     // 指派时间
  }
}
```

#### 4.2.2 调整任务指派

- **接口说明**：组织机构负责人调整任务指派的成员
- **请求方法**：POST
- **请求路径**：`/api/pc/v1/tasks/assignments/{taskAssignId}/reassign`
- **请求参数**：

```json
{
  "employeeId": "USER004",                // 新指派的成员ID
  "remarks": "请尽快完成填报"               // 备注说明
}
```

- **响应参数**：

```json
{
  "code": 0,
  "msg": "调整成功",
  "data": {
    "taskAssignId": "ASSIGN20250115001",    // 任务分配ID
    "reassignTime": "2025-01-17 10:30:00"   // 重新指派时间
  }
}
```

### 4.3 任务填报接口

#### 4.3.1 保存草稿

- **接口说明**：组织机构成员保存填报草稿
- **请求方法**：POST
- **请求路径**：`/api/pc/v1/tasks/reports/draft/save`
- **请求参数**：

```json
{
  "taskId": "TASK20250115001",             // 任务ID
  "taskAssignId": "ASSIGN20250115001",     // 任务分配ID
  "orgId": "ORG001",                      // 组织机构ID
  "employeeId": "USER003",                // 填报人员ID
  "prodPeriod": "PRO",                    // 生产阶段，PRO=采气，INJ=注气
  "prodYearMonth": "2025-02",             // 生产计划年月
  "periodReports": [                       // 时段填报数据
    {
      "periodStartDate": "2025-02-01",     // 时段开始时间
      "periodEndDate": "2025-02-10",       // 时段结束时间
      "periodDays": 10,                    // 时段天数
      "dailyGasVolume": 15.5               // 日采气/注气量(万方)
    },
    {
      "periodStartDate": "2025-02-11",     // 时段开始时间
      "periodEndDate": "2025-02-28",       // 时段结束时间
      "periodDays": 18,                    // 时段天数
      "dailyGasVolume": 18.2               // 日采气/注气量(万方)
    }
  ],
  "remarks": "2月11日起增加采气量"          // 备注
}
```

- **响应参数**：

```json
{
  "code": 0,
  "msg": "保存成功",
  "data": {
    "draftId": "DRAFT20250118001",          // 草稿ID
    "saveTime": "2025-01-18 14:30:00"       // 保存时间
  }
}
```

#### 4.3.2 提交填报

- **接口说明**：组织机构成员提交填报内容
- **请求方法**：POST
- **请求路径**：`/api/pc/v1/tasks/reports/submit`
- **请求参数**：与保存草稿接口相同
- **响应参数**：

```json
{
  "code": 0,
  "msg": "提交成功",
  "data": {
    "taskId": "TASK20250115001",            // 任务ID
    "submitTime": "2025-01-19 16:30:00"     // 提交时间
  }
}
```

#### 4.3.3 获取草稿

- **接口说明**：获取已保存的草稿内容
- **请求方法**：GET
- **请求路径**：`/api/pc/v1/tasks/reports/draft`
- **请求参数**：
  - taskId: 任务ID
  - taskAssignId: 任务分配ID
  - employeeId: 填报人员ID
- **响应参数**：

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "draftId": "DRAFT20250118001",          // 草稿ID
    "taskId": "TASK20250115001",            // 任务ID
    "taskAssignId": "ASSIGN20250115001",    // 任务分配ID
    "orgId": "ORG001",                     // 组织机构ID
    "employeeId": "USER003",               // 填报人员ID
    "prodPeriod": "PRO",                   // 生产阶段
    "prodYearMonth": "2025-02",            // 生产计划年月
    "periodReports": [                      // 时段填报数据
      {
        "periodStartDate": "2025-02-01",    // 时段开始时间
        "periodEndDate": "2025-02-10",      // 时段结束时间
        "periodDays": 10,                   // 时段天数
        "dailyGasVolume": 15.5              // 日采气/注气量(万方)
      },
      {
        "periodStartDate": "2025-02-11",    // 时段开始时间
        "periodEndDate": "2025-02-28",      // 时段结束时间
        "periodDays": 18,                   // 时段天数
        "dailyGasVolume": 18.2              // 日采气/注气量(万方)
      }
    ],
    "remarks": "2月11日起增加采气量",         // 备注
    "createTime": "2025-01-18 14:30:00",    // 创建时间
    "updateTime": "2025-01-18 15:45:00"     // 更新时间
  }
}
```

### 4.4 任务查询接口

#### 4.4.1 我的任务列表

- **接口说明**：获取当前用户相关的任务列表
- **请求方法**：POST
- **请求路径**：`/api/pc/v1/tasks/page`
- **请求参数**：

```json
{
  "pageNum": 1,                           // 页码
  "pageSize": 10,                         // 每页记录数
  "taskType": "todo",                     // 任务类型：todo(待办)、done(已办)、participate(参与的)、created(创建的)
  "orgId": "ORG001",                      // 组织机构ID，可选
  "subTaskClass": "月度采气计划",           // 任务小类，可选
  "planYearMonth": "2025-02",             // 计划年月，可选
  "taskStatus": "2",                      // 任务状态，可选：1(已完成)、2(进行中)、3(已超期)
  "startTime": "2025-01-01",              // 开始时间，可选
  "endTime": "2025-01-31"                 // 结束时间，可选
}
```

- **响应参数**：

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "data": [
      {
        "taskId": "TASK20250115001",         // 任务ID
        "taskName": "2025年2月采气计划",      // 任务名称
        "subTaskClass": "月度采气计划",        // 任务小类
        "planYearMonth": "2025-02",          // 计划年月
        "startTime": "2025-01-15 00:00:00",  // 开始时间
        "endTime": "2025-01-25 23:59:59",    // 结束时间
        "orgName": "XX储气库",               // 组织机构名称
        "managerName": "张三",               // 负责人姓名
        "employeeName": "李四",              // 执行人姓名
        "taskStatus": "2",                   // 任务状态
        "taskStatusName": "进行中",           // 任务状态名称
        "createTime": "2025-01-15 10:30:00"   // 创建时间
      },
      // 更多任务...
    ],
    "pageNum": 1,                          // 当前页码
    "pageSize": 10,                        // 每页记录数
    "totalCount": 25,                      // 总记录数
    "totalPage": 3                         // 总页数
  }
}
```

#### 4.4.2 任务详情

- **接口说明**：获取任务详细信息
- **请求方法**：GET
- **请求路径**：`/api/pc/v1/tasks/{taskId}`
- **请求参数**：无
- **响应参数**：

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "taskId": "TASK20250115001",            // 任务ID
    "taskClass": "计划上报",                 // 任务大类
    "subTaskClass": "月度采气计划",           // 任务小类
    "taskName": "2025年2月采气计划",          // 任务名称
    "planYearMonth": "2025-02",             // 计划年月
    "startTime": "2025-01-15 00:00:00",     // 开始时间
    "endTime": "2025-01-25 23:59:59",       // 结束时间
    "taskDesc": "请各单位按时填报2月采气计划",   // 任务说明
    "taskStatus": "2",                      // 任务状态
    "taskStatusName": "进行中",              // 任务状态名称
    "sender": "王五",                       // 发布人
    "sendDate": "2025-01-15 10:30:00",      // 发布时间
    "isSupervised": "1",                    // 是否多人协作
    "attachmentFileId": "",                 // 附件ID
    "finishTime": null,                     // 完成时间
    "assignments": [                        // 任务分配信息
      {
        "taskAssignId": "ASSIGN20250115001", // 任务分配ID
        "orgId": "ORG001",                  // 组织机构ID
        "orgName": "XX储气库",               // 组织机构名称
        "managerId": "USER001",             // 负责人ID
        "managerName": "张三",              // 负责人姓名
        "employeeId": "USER003",            // 执行人ID
        "employeeName": "李四",             // 执行人姓名
        "taskStatus": "2",                  // 任务状态
        "taskStatusName": "进行中",          // 任务状态名称
        "finishTime": null                  // 完成时间
      },
      // 更多分配信息...
    ],
    "createTime": "2025-01-15 10:30:00",     // 创建时间
    "createUser": "王五",                    // 创建用户
    "updateTime": "2025-01-15 10:30:00",     // 更新时间
    "updateUser": "王五"                      // 更新用户
  }
}
```

#### 4.4.3 任务日志

- **接口说明**：获取任务操作日志
- **请求方法**：GET
- **请求路径**：`/api/pc/v1/tasks/{taskId}/logs`
- **请求参数**：无
- **响应参数**：

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "logId": "LOG20250115001",             // 日志ID
      "taskId": "TASK20250115001",           // 任务ID
      "taskAssignId": "",                    // 任务分配ID
      "operationType": "创建",                // 操作类型
      "operationTime": "2025-01-15 10:30:00", // 操作时间
      "operatorId": "USER005",               // 操作人ID
      "operatorName": "王五",                 // 操作人姓名
      "operationDesc": "创建任务并分配",         // 操作描述
      "duration": 0                          // 耗时(小时)
    },
    {
      "logId": "LOG20250116001",             // 日志ID
      "taskId": "TASK20250115001",           // 任务ID
      "taskAssignId": "ASSIGN20250115001",   // 任务分配ID
      "operationType": "指派",                // 操作类型
      "operationTime": "2025-01-16 09:30:00", // 操作时间
      "operatorId": "USER001",               // 操作人ID
      "operatorName": "张三",                 // 操作人姓名
      "operationDesc": "指派任务给李四",         // 操作描述
      "duration": 23                         // 耗时(小时)
    },
    // 更多日志...
  ]
}
```

#### 4.4.4 填报结果查询

- **接口说明**：获取任务填报结果
- **请求方法**：GET
- **请求路径**：`/api/pc/v1/tasks/{taskId}/reports`
- **请求参数**：
  - orgId: 组织机构ID
- **响应参数**：

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "taskId": "TASK20250115001",            // 任务ID
    "orgId": "ORG001",                     // 组织机构ID
    "orgName": "XX储气库",                  // 组织机构名称
    "prodPeriod": "PRO",                   // 生产阶段
    "prodYearMonth": "2025-02",            // 生产计划年月
    "dailyReports": [                      // 日报表数据
      {
        "prodDate": "2025-02-01",           // 生产日期
        "prodGasVolume": 15.5               // 计划注/采气量(万方)
      },
      {
        "prodDate": "2025-02-02",           // 生产日期
        "prodGasVolume": 15.5               // 计划注/采气量(万方)
      },
      // 更多日期数据...
    ],
    "summary": {
      "totalDays": 28,                     // 总天数
      "totalVolume": 480.6,                 // 总计划量(万方)
      "avgDailyVolume": 17.16               // 日均计划量(万方)
    },
    "submitTime": "2025-01-19 16:30:00",    // 提交时间
    "submitter": "李四"                     // 提交人
  }
}
```

## 5. 组织机构管理接口

### 5.1 组织机构查询接口

#### 5.1.1 获取组织机构列表

- **接口说明**：获取可用于任务分配的组织机构列表
- **请求方法**：GET
- **请求路径**：`/api/pc/v1/organizations`
- **请求参数**：无
- **响应参数**：

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "orgId": "ORG001",                    // 组织机构ID
      "orgName": "XX储气库",                 // 组织机构名称
      "orgCode": "XXCQK",                   // 组织机构编码
      "orgType": "储气库",                  // 组织机构类型
      "orgLevel": "1",                     // 组织机构级别
      "parentId": "",                      // 上级组织机构ID
      "parentName": "",                    // 上级组织机构名称
      "status": "1",                       // 状态：1-正常，0-停用
      "createTime": "2024-12-01 10:00:00"   // 创建时间
    },
    {
      "orgId": "ORG002",                    // 组织机构ID
      "orgName": "YY储气库",                 // 组织机构名称
      "orgCode": "YYCQK",                   // 组织机构编码
      "orgType": "储气库",                  // 组织机构类型
      "orgLevel": "1",                     // 组织机构级别
      "parentId": "",                      // 上级组织机构ID
      "parentName": "",                    // 上级组织机构名称
      "status": "1",                       // 状态：1-正常，0-停用
      "createTime": "2024-12-01 10:00:00"   // 创建时间
    },
    // 更多组织机构...
  ]
}
```

#### 5.1.2 获取组织机构详情

- **接口说明**：获取组织机构详细信息
- **请求方法**：GET
- **请求路径**：`/api/pc/v1/organizations/{orgId}`
- **请求参数**：无
- **响应参数**：

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "orgId": "ORG001",                     // 组织机构ID
    "orgName": "XX储气库",                  // 组织机构名称
    "orgCode": "XXCQK",                    // 组织机构编码
    "orgType": "储气库",                   // 组织机构类型
    "orgFunction": "生产单位",              // 机构职能
    "orgLevel": "1",                      // 组织机构级别
    "orgAdmLevel": "2",                   // 机构行政级别
    "parentId": "",                       // 上级组织机构ID
    "parentName": "",                     // 上级组织机构名称
    "address": "XX省XX市XX区",              // 地址
    "status": "1",                        // 状态：1-正常，0-停用
    "remarks": "XX储气库负责XX区域天然气储存",  // 备注
    "createTime": "2024-12-01 10:00:00",   // 创建时间
    "updateTime": "2024-12-10 15:30:00"    // 更新时间
  }
}
```

#### 5.1.3 获取组织机构下属成员

- **接口说明**：获取组织机构下的人员列表
- **请求方法**：GET
- **请求路径**：`/api/pc/v1/organizations/{orgId}/members`
- **请求参数**：无
- **响应参数**：

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "userId": "USER001",                  // 用户ID
      "loginName": "zhangsan",              // 登录名
      "displayName": "张三",                // 显示名称
      "userType": "1",                     // 用户类型：1-管理员，2-负责人，3-普通成员
      "email": "<EMAIL>",      // 邮箱
      "phoneNumber": "13800138001",         // 手机号
      "status": "1",                       // 状态：1-正常，0-停用
      "createTime": "2024-12-01 10:00:00"   // 创建时间
    },
    {
      "userId": "USER003",                  // 用户ID
      "loginName": "lisi",                  // 登录名
      "displayName": "李四",                // 显示名称
      "userType": "3",                     // 用户类型：1-管理员，2-负责人，3-普通成员
      "email": "<EMAIL>",          // 邮箱
      "phoneNumber": "13800138003",         // 手机号
      "status": "1",                       // 状态：1-正常，0-停用
      "createTime": "2024-12-01 10:00:00"   // 创建时间
    },
    // 更多成员...
  ]
}
```

### 5.2 组织机构负责人接口

#### 5.2.1 获取组织机构负责人列表

- **接口说明**：获取组织机构的负责人列表
- **请求方法**：GET
- **请求路径**：`/api/pc/v1/organizations/{orgId}/managers`
- **请求参数**：无
- **响应参数**：

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "userId": "USER001",                  // 用户ID
      "loginName": "zhangsan",              // 登录名
      "displayName": "张三",                // 显示名称
      "email": "<EMAIL>",      // 邮箱
      "phoneNumber": "13800138001",         // 手机号
      "status": "1",                       // 状态：1-正常，0-停用
      "createTime": "2024-12-01 10:00:00"   // 创建时间
    },
    {
      "userId": "USER002",                  // 用户ID
      "loginName": "wangwu",                // 登录名
      "displayName": "王五",                // 显示名称
      "email": "<EMAIL>",        // 邮箱
      "phoneNumber": "13800138002",         // 手机号
      "status": "1",                       // 状态：1-正常，0-停用
      "createTime": "2024-12-01 10:00:00"   // 创建时间
    }
  ]
}
```

## 6. 数据字典接口

### 6.1 任务状态字典

- **接口说明**：获取任务状态字典
- **请求方法**：GET
- **请求路径**：`/api/pc/v1/dict/task-status`
- **请求参数**：无
- **响应参数**：

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "label": "已完成",
      "value": "1"
    },
    {
      "label": "进行中",
      "value": "2"
    },
    {
      "label": "已超期",
      "value": "3"
    }
  ]
}
```

### 6.2 任务小类字典

- **接口说明**：获取任务小类字典
- **请求方法**：GET
- **请求路径**：`/api/pc/v1/dict/sub-task-class`
- **请求参数**：无
- **响应参数**：

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "label": "月度采气计划",
      "value": "MONTHLY_EXTRACTION_PLAN"
    },
    {
      "label": "月度注气计划",
      "value": "MONTHLY_INJECTION_PLAN"
    }
  ]
}
```

### 6.3 生产阶段字典

- **接口说明**：获取生产阶段字典
- **请求方法**：GET
- **请求路径**：`/api/pc/v1/dict/prod-period`
- **请求参数**：无
- **响应参数**：

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "label": "采气",
      "value": "PRO"
    },
    {
      "label": "注气",
      "value": "INJ"
    }
  ]
}
```

### 6.4 用户类型字典

- **接口说明**：获取用户类型字典
- **请求方法**：GET
- **请求路径**：`/api/pc/v1/dict/user-type`
- **请求参数**：无
- **响应参数**：

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "label": "管理员",
      "value": "1"
    },
    {
      "label": "负责人",
      "value": "2"
    },
    {
      "label": "普通成员",
      "value": "3"
    }
  ]
}
```

## 7. 错误码定义

| 错误码 | 错误消息 | 描述 |
|-------|------|------|
| 0 | 成功 | 请求成功 |
| 10003 | 未登录 | 用户未登录或登录已过期 |
| 10008 | 无访问权限 | 用户无权访问该资源 |
| 50001 | 会话超时 | 用户会话超时 |
| 50002 | token不合法 | 提供的认证令牌无效 |
| 50003 | 参数有误 | 请求参数错误 |
| 50004 | 参数不完整 | 缺少必要的请求参数 |
| 50005 | 参数不合法 | 请求参数格式或值域不符合要求 |
| 50006 | 数据库连接异常 | 数据库连接异常 |
| 50008 | 服务端错误 | 服务器内部错误 |
| 50010 | 无数据 | 请求的资源不存在 |
| 50011 | 记录已存在 | 请求创建的资源已存在 |
| 50012 | 删除错误 | 删除操作失败 |
| 50013 | 修改错误 | 修改操作失败 |
| 50014 | 系统错误，请重试 | 系统错误 |

## 8. 接口示例

### 8.1 创建任务示例

#### 请求示例

```http
POST /api/pc/v1/tasks/create HTTP/1.1
Host: example.com
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "taskClass": "计划上报",
  "subTaskClass": "月度采气计划",
  "taskName": "2025年2月采气计划",
  "planYearMonth": "2025-02",
  "startTime": "2025-01-15 00:00:00",
  "endTime": "2025-01-25 23:59:59",
  "taskDesc": "请各单位按时填报2月采气计划",
  "assignments": [
    {
      "orgId": "ORG001",
      "managerId": "USER001"
    },
    {
      "orgId": "ORG002",
      "managerId": "USER002"
    }
  ]
}
```

#### 响应示例

```http
HTTP/1.1 200 OK
Content-Type: application/json;charset=UTF-8

{
  "code": 0,
  "msg": "创建成功",
  "data": {
    "taskId": "TASK20250115001",
    "createTime": "2025-01-15 10:30:00"
  }
}
```

### 8.2 任务列表查询示例

#### 请求示例

```http
POST /api/pc/v1/tasks/page HTTP/1.1
Host: example.com
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "taskType": "todo",
  "subTaskClass": "月度采气计划",
  "planYearMonth": "2025-02",
  "taskStatus": "2"
}
```

#### 响应示例

```http
HTTP/1.1 200 OK
Content-Type: application/json;charset=UTF-8

{
  "code": 0,
  "msg": "",
  "data": {
    "data": [
      {
        "taskId": "TASK20250115001",
        "taskName": "2025年2月采气计划",
        "subTaskClass": "月度采气计划",
        "planYearMonth": "2025-02",
        "startTime": "2025-01-15 00:00:00",
        "endTime": "2025-01-25 23:59:59",
        "orgName": "XX储气库",
        "managerName": "张三",
        "employeeName": "李四",
        "taskStatus": "2",
        "taskStatusName": "进行中",
        "createTime": "2025-01-15 10:30:00"
      }
    ],
    "pageNum": 1,
    "pageSize": 10,
    "totalCount": 1,
    "totalPage": 1
  }
}
```

### 8.3 保存草稿示例

#### 请求示例

```http
POST /api/pc/v1/tasks/reports/draft/save HTTP/1.1
Host: example.com
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "taskId": "TASK20250115001",
  "taskAssignId": "ASSIGN20250115001",
  "orgId": "ORG001",
  "employeeId": "USER003",
  "prodPeriod": "PRO",
  "prodYearMonth": "2025-02",
  "periodReports": [
    {
      "periodStartDate": "2025-02-01",
      "periodEndDate": "2025-02-10",
      "periodDays": 10,
      "dailyGasVolume": 15.5
    },
    {
      "periodStartDate": "2025-02-11",
      "periodEndDate": "2025-02-28",
      "periodDays": 18,
      "dailyGasVolume": 18.2
    }
  ],
  "remarks": "2月11日起增加采气量"
}
```

#### 响应示例

```http
HTTP/1.1 200 OK
Content-Type: application/json;charset=UTF-8

{
  "code": 0,
  "msg": "保存成功",
  "data": {
    "draftId": "DRAFT20250118001",
    "saveTime": "2025-01-18 14:30:00"
  }
}
```