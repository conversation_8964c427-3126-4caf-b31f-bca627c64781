1. 通用规范和要求
1.1 模型设计规范
1、数据模型设计标准为依据；
2、PG库中表名及字段名等对象默认小写，因此所有库表对象英文名称在设计时均采用小写字母和数字；
3、数据模型设计采用物理模型设计模式，通用类实体属性（Attribute）的类型尽量采用在域（Domain）中定义的类型，如主键ID、井号等；
4、非结构化数据存储采用文件服务管理（FSS）方式，应尽量避免在数据库中应用TEXT类型字段存储大数据文件；
5、数据库中相关对象均应包含中文注释信息（Comment），包括数据表、数据项等。
1.2 环境要求
数据库产品和版本：PostgreSQL 14.17。
数据模型设计工具和版本：PowerDesigner 16.7 SP05。
1.3 运维规范
1、在设计与开发阶段，由项目指定的数据库管理员对开发测试环境和生产环境数据库进行维护与更新，分配给开发人员的数据库账号仅具备数据的增删改查权限，不具备数据库表结构和数据对象的创建和维护权限；
2、如对数据表结构进行修改，应先更新数据库结构设计模型文件及相关设计文档，并通知数据库管理员归档后，将相关SQL脚本发给数据库管理员，由其同步维护项目统一的数据模型文件及文档，经确定SQL脚本无误后再对开发测试环境或生产环境数据库执行；
3、禁止由模型设计工具直接连数据库进行数据库操作，可由设计工具生成SQL语句，提交数据库管理员负责执行；
4、数据模型的设计文件及文档由数据库管理员负责在进行维护更新。
2. 数据对象设计规范
2.1 数据表
1、数据表名即模型实体名，通常用代码表示。采用不定长分段层次码，代码总长度不超过30个字符，代码结构如下：
XX_XXX_XXXX_XXXX_XXXX _ ... ....
第一段   第二段                 第三段
第一层（专业分类码），由 2-3个专业分类英文单词缩写字母组成实体名前缀，前缀码与第二层码之间用字符“_”进行连接。
专业分类码：基本实体（CD）、信息管理（IM）、人力资源（HR）等，见表1。
第二层（子类限定词），有三个子类英文单词缩写字母组成，缩写代码为：
基础数据（BAS）、设计数据（DES）、施工数据（OPS）、成果数据（ACH）、实时数据（RET）、应用数据（APP）、报表数据（RPT）等。
第三层（实体名称码）：由数据表名称关键英文单词组成，建议用完整的单词，总长度最多不能超过23个字符，如果导致数据表名称长度超过23个字符，则从最后一个单词开始，依次向前采用该单词的缩写（如果没有约定的缩写，则采用该单词前3-4个字母来表示），关键英文单词之间用字符“_”进行连接。
当模型数据表英文名称单词数量大于1时，命名格式为所属业务包名（2位）_单词缩写1_单词缩写2_单词缩写3_单词缩写4；
当单词数量等于1时，命名格式为所属业务包名（2位）_单词全拼。
表1  数据表分类代码
序号
数据分类(中文)
数据分类(英文)
英文单词缩写
1
基本实体
core_data
cd_
3
信息管理
information_management
im_
29
人力资源
human_resources
hr_
30
质量计量安全环保
quality_health
qh_
32
工程建设
engineering
eo_
33
综合管理
cooperative
co_
34
稳安安保
maintain_stability_and_peace
mp_
37
统计表(?)
statistical_list
sl_
38
公共业务
production_operation
pr_
39
任务模型(?)
project_manager
pm_
40
文档管理(?)
file_system
fs_
41
系统管理
system
sys_

表2  数据表名称代码命名示例
数据分类
数据分类码
数据表名称
数据表代码
主数据
cd_
井
cd_well


组织机构 (CO)
cd_organization


2、每个表（包含系统表）中应包含记录数据创新及更新信息的列属性（包括：逻辑删除标识（N存在Y 删除）bsflag，创建者create_user，创建时间create_date，更新者update_user，更新时间update_date），具体如下表所示。
表3  数据表固定字段列表
序号
字段中文名
字段code
数据类型
默认值
非空
注释
1
逻辑删除标识
bsflag
char(1)
N
Y
N存在，Y删除
2
创建者
create_user
varchar(64)

Y

3
创建时间
create_date
timestamp

Y

4
更新者
update_user
varchar(64)

Y

5
更新时间
update_date
timestamp

Y


2.2 数据项
数据项是数据结构定义的最小单元，本数据表结构对数据项的定义包含以下七个方面的内容：
1、数据项名称采用小写关键英文单词和字符“_”进行混合编码，英文单词之间用字符“_” 进行连接。
表4  数据项名称编码示例
所属数据表
数据项名称
数据项代码
用户数据(cd_user_info)



2、模型中所有表（实体）的非业务主键（逻辑主键）字段只能有一个，主键ID代码采用固定长度32个字符(char(32))，主键英文字段名采用xxxxx（英文表名）_id的方式进行命名，并按照UUID生成规则进行生成，如果主键字段名称长度超过30位，则去掉表名业务分类前缀和下划线。
如cd_user_data表的主键ID字段命名为：cd_user_data_id。
3、涉及到主数据的逻辑ID数据项要保留，可不作为主键，根据实际应用场景可要求非空；
4、重要的数据项往前排同类的数据项放在一块，例如，表达深度段的字段放在一起，先小后大，顶深在前底深在后；
5、对于非空值，需要根据业务中对数据的非空约束进行设置，应尽量避免一张表中仅有主键字段非空的情况。
6、数据项中文名称应能够根据中文名称了解表中存储的业务数据内容，且名称不能包含括号、横杠、逗号、句号及特殊符号等，相关字段注释、补充说明及代码解释应放在数据项备注（Comment）中。
7、数据项类型：数据项的实际数据类型，为PostgreSQL支持的数据类型，常用主要包括以下六类：
①char(n)：定长字符类型，长度为定义n个，一般用于定义数据表的主键(外键)；
②varchar(n)：不定长字符类型，表示该字段最大可存储的字符长度为n，当存储的实际值不够n，则系统中只存储实际长度；
③numeric(n,m)：数字型，可存储整型、浮点型、双精度数等所有数字型数值。其中n为数值的总长度，m为数值的精度，即小数点后面的位数，当m=0时，该数值为整型；
④timestamp：日期时间型，可以存储到毫秒级，完整格式为：yyyy-mm-dd hr:ms:ss:sss，具体存储在数据库中的数据可以根据前端定义的数据格式进行存储；
⑤int4：整数型，数据范围为[-21474836478～2147483647‬]；
⑥float8：浮点型，双精度（8字节）浮点数。
2.3 其他数据对象
2.3.1 主键
1、主键对象名称用pk_表名称的方式进行命名。主键名长度不能超过30个字符。如果过长，可对表名进行缩写。缩写规则同表名的缩写规则；
2、主键名用小写的英文单词来表示。
2.3.2 外键
1、外键字段根据父表的数量可以为多个；
2、外键根据业务场景可为非空；
3、外键关系对象名称格式为“fk_子表简写名_ref_主表简写名”；
4、子表中外键数据项英文名称命名时，如果是主表的主键，则可设计为和主表的主键字段一致，如果不是主表的主键字段，则设计为“主表表名简写_主表字段名”。
2.3.3 视图
视图命名规则参照表命名规则，与表命名的区别是加前缀v_。
例：v_cd_user_info。
2.3.4 存储过程
存储过程命名规则参照表命名规则，与表命名的区别是加前缀sp_，加上业务描述（动宾结构）。
例：sp_cd_check_user_auth。
2.3.5 触发器
触发器以tr_为前缀，加上表名及触发条件（i/u/d）。
例：tr_user_d。
2.3.6 函数
函数以fn_为前缀，加上函数描述（动宾结构）。
例：fn_create_id。
2.4 实体关系设计（ER图）
数据模型中使用实体联系图来描述数据表之间的逻辑关系，主要通过对实体和关系进行描述，样式如下所示：
[图片]
图1  实体关系图图例
方框用来表示实体(数据表)，带箭头的直线或曲线用来表示实体之间的关系(逻辑关系)。方框中的内容分为两个部分，上部为数据表中文名称，下部为数据表的主外键字段列表，PK代表主键，FK代表外键，多个外键用数字依次标识，其它字段在方框中可不显示。比如井筒数据表中，“井ID”和“井筒ID”为该数据表的联合主键，其中“井ID”同时是指向井数据表的外键。
箭头线表示两张数据表之间的逻辑关系，箭头指向的数据表为父表，箭头出发的数据表为子表。如图1中的井数据表是父表，井筒数据表是子表，表达的含义是一对多的逻辑关系，即一口井可以包含1个或多个井筒。
[图片]
图2  EPDM主数据实体关系图图例