# 任务中心系统数据表字典

## 1. 新增表

### 1.1 储气库生产运行计划草稿表（op_ugs_inj_pro_plan_draft）

| 字段名 | 类型 | 必填 | 描述 | 备注 |
|-------|------|------|------|------|
| draft_id | varchar(32) | 是 | 草稿ID | 主键 |
| task_id | varchar(32) | 是 | 任务ID | 外键关联：pm_base_task |
| task_assign_id | varchar(32) | 是 | 任务分配ID | 外键关联：pm_task_assignment |
| org_id | varchar(32) | 是 | 组织机构ID | 外键关联：cd_organization |
| employee_id | varchar(32) | 是 | 填报人员ID | 外键关联：sys_user |
| prod_period | varchar(32) | 是 | 生产阶段 | 区分注气("INJ")或采气("PRO") |
| prod_year_month | varchar(7) | 是 | 生产计划年月 | 格式：YYYY-MM |
| period_start_date | timestamp(6) | 是 | 时段开始时间 | 具体到天 |
| period_end_date | timestamp(6) | 是 | 时段结束时间 | 具体到天 |
| period_days | numeric(3,0) | 是 | 时段天数 | 自动计算 |
| daily_gas_volume | numeric(15,6) | 是 | 日采气/注气量 | 单位：万方 |
| remarks | varchar(256) | 否 | 备注 | 可选 |
| create_time | timestamp(6) | 是 | 创建时间 | |
| update_time | timestamp(6) | 是 | 更新时间 | |
| bsflag | numeric(1,0) | 是 | 数据逻辑删除标识 | 1=在用，-5=废弃 |

### 1.2 任务日志表（pm_task_log）

| 字段名 | 类型 | 必填 | 描述 | 备注 |
|-------|------|------|------|------|
| log_id | varchar(32) | 是 | 日志ID | 主键 |
| task_id | varchar(32) | 是 | 任务ID | 外键关联：pm_base_task |
| task_assign_id | varchar(32) | 是 | 任务分配ID | 外键关联：pm_task_assignment |
| operation_type | varchar(32) | 是 | 操作类型 | 如创建、分配、指派、提交等 |
| operation_time | timestamp(6) | 是 | 操作时间 | |
| operator_id | varchar(32) | 是 | 操作人 ID | 外键关联：sys_user |
| operation_desc | varchar(256) | 否 | 操作描述 | |
| bsflag | numeric(1,0) | 是 | 数据逻辑删除标识 | 1=在用，-5=废弃 |
| create_date | timestamp(6) | 是 | 创建时间 | |
| create_user_id | varchar(64) | 是 | 创建用户 | |
| update_date | timestamp(6) | 是 | 更新时间 | |
| update_user_id | varchar(64) | 是 | 更新用户 | |
| tenant_id | varchar(32) | 否 | 租户ID | |

## 2. 相关表（dawfl.sql中已有表）

### 2.1 储气库生产运行月计划表（op_ugs_inj_pro_plan_month）

| 字段名 | 类型 | 必填 | 描述 | 备注 |
|-------|------|------|------|------|
| inj_pro_issued_plan_id | varchar(32) | 是 | 储气库生产运行月计划ID | 主键 |
| org_id | varchar(32) | 是 | 组织机构ID | 关联cd_organization表 |
| prod_period | varchar(32) | 是 | 生产阶段 | 区分注气("INJ")或采气("PRO") |
| prod_date | timestamp(6) | 是 | 生产日期 | 具体到天 |
| prod_gas_volume | numeric(15,6) | 否 | 计划注/采气量 | 单位：万方 |
| bsflag | numeric(1,0) | 是 | 数据逻辑删除标识 | 1=在用，-5=废弃 |
| remarks | varchar(2000) | 否 | 备注 | |
| create_date | timestamp(6) | 是 | 创建时间 | |
| create_user_id | varchar(64) | 是 | 创建用户 | |
| create_app_id | varchar(64) | 否 | 数据来源系统 | |
| update_date | timestamp(6) | 是 | 更新时间 | |
| update_user_id | varchar(64) | 是 | 更新用户 | |
| check_date | timestamp(6) | 否 | 审核时间 | |
| check_user_id | varchar(64) | 否 | 审核用户 | |
| data_source | varchar(512) | 否 | 数据来源表CODE | |
| source_data_id | varchar(256) | 否 | 数据来源表主键 | |
| data_region | varchar(2) | 否 | 油田标识 | |
| tenant_id | varchar(36) | 否 | 租户ID | |

### 2.2 任务基本信息表（pm_base_task）

| 字段名 | 类型 | 必填 | 描述 | 备注 |
|-------|------|------|------|------|
| task_id | varchar(32) | 是 | 任务ID | 主键 |
| parent_task_id | varchar(32) | 否 | 父任务ID | |
| org_id | varchar(32) | 是 | 组织机构ID | |
| task_class | varchar(32) | 是 | 任务大类 | 固定为"计划上报" |
| sub_task_class | varchar(32) | 是 | 任务小类 | "月度采气计划"或"月度注气计划" |
| task_name | varchar(128) | 是 | 任务名称 | |
| task_desc | varchar(256) | 否 | 任务描述 | |
| start_time | timestamp(6) | 是 | 要求开始日期 | |
| end_time | timestamp(6) | 是 | 要求结束日期 | |
| sender | varchar(32) | 否 | 发布人 | |
| send_date | timestamp(6) | 否 | 发布时间 | |
| is_supervised | varchar(1) | 否 | 是否多人协作 | 1是，0否 |
| attachment_file_id | varchar(2000) | 否 | 附件（多个） | |
| task_status | varchar(1) | 是 | 任务状态 | 1已完成；2进行中；3已超期 |
| finish_time | timestamp(6) | 否 | 任务完成时间 | |
| bsflag | numeric(1,0) | 是 | 数据逻辑删除标识 | 1=在用，-5=废弃 |
| remarks | varchar(2000) | 否 | 备注 | |
| create_date | timestamp(6) | 否 | 创建时间 | |
| create_user_id | varchar(64) | 否 | 创建用户 | |
| create_app_id | varchar(64) | 否 | 数据来源系统 | |
| update_date | timestamp(6) | 否 | 更新时间 | |
| update_user_id | varchar(64) | 否 | 更新用户 | |
| check_date | timestamp(6) | 否 | 审核时间 | |
| check_user_id | varchar(64) | 否 | 审核用户 | |
| data_source | varchar(512) | 否 | 数据来源表CODE | |
| source_data_id | varchar(256) | 否 | 数据来源表主键 | |
| data_region | varchar(2) | 否 | 油田标识 | |
| tenant_id | varchar(32) | 否 | 租户ID | |

### 2.3 任务分配表（pm_task_assignment）

| 字段名            | 类型 | 必填 | 描述        | 备注 |
|----------------|------|------|-----------|------|
| task_assign_id | varchar(32) | 是 | 任务分配ID    | 主键 |
| task_id        | varchar(32) | 是 | 任务ID      | 外键关联：pm_base_task |
| org_id         | varchar(32) | 是 | 组织机构ID    | 外键关联：cd_organization |
| manager_id     | varchar(32) | 是 | 负责人员ID    | 外键关联：sys_user |
| employee_id    | varchar(32) | 是 | 人员ID      | 外键关联：sys_user |
| task_status    | varchar(1) | 是 | 任务状态      | 1已完成；2进行中；3已超期 |
| finish_time    | timestamp(6) | 否 | 任务完成时间    | |
| bsflag         | numeric(1,0) | 是 | 数据逻辑删除标识  | 1=在用，-5=废弃 |
| remarks        | varchar(2000) | 否 | 备注        | |
| create_date    | timestamp(6) | 否 | 创建时间      | |
| create_user_id | varchar(64) | 否 | 创建用户      | |
| create_app_id  | varchar(64) | 否 | 数据来源系统    | |
| update_date    | timestamp(6) | 否 | 更新时间      | |
| update_user_id | varchar(64) | 否 | 更新用户      | |
| check_date     | timestamp(6) | 否 | 审核时间      | |
| check_user_id  | varchar(64) | 否 | 审核用户      | |
| data_source    | varchar(512) | 否 | 数据来源表CODE | |
| source_data_id | varchar(256) | 否 | 数据来源表主键   | |
| data_region    | varchar(2) | 否 | 油田标识      | |
| tenant_id      | varchar(32) | 否 | 租户ID      | |

## 3. 数据表关系说明

### 3.1 草稿表与正式表关系
- op_ugs_inj_pro_plan_draft（储气库生产运行计划草稿表）用于存储用户填报的草稿数据
- 当用户提交时，系统将草稿表中的数据转换为按日期存储的格式，保存到op_ugs_inj_pro_plan_month（储气库生产运行月计划表）中
- 草稿表按时段存储数据，而正式表按日期存储数据

### 3.2 任务日志与任务关系
- pm_task_log（任务日志表）记录任务全生命周期的操作日志
- 通过task_id字段关联到pm_base_task（任务基本信息表）
- 记录任务的创建、分配、指派、提交等各个环节的操作信息

### 3.3 数据转换逻辑
当用户提交草稿时，系统需要将按时段填报的数据转换为按日期存储的数据：
1. 遍历草稿表中的每条时段记录
2. 对于每个时段，根据开始日期和结束日期，生成该时段内每一天的记录
3. 每天的采气/注气量等于时段的日采气/注气量
4. 将生成的日期记录批量插入到op_ugs_inj_pro_plan_month表中

## 4. 数据字典维护说明

1. 本数据字典应随系统变更及时更新
2. 表结构变更需经过评审，并在变更后更新本文档
3. 字段含义变更需在本文档中明确说明
4. 文档版本号应与系统版本保持一致
