接口命名规范

Restful:
1）URL具有强可读性和自描述性
2）规范化请求过程和返回结果
3）资源描述与视图的松耦合
4）可提供OpenAPI，便于第三方系统集成，提高互操作性
5）提供无状态的服务接口，降低复杂度，可提高应用的水平扩展性
一、PC端移动端版本号:
PC端版本号以pc开始；
移动端版本号以mobile开始；
服务端版本号已service开始；
【GET】 /api/pc/v1/users/{user_id} // 版本 v1 的查询用户列表的 API 接口
【GET】 /api/pc/v2/users/{user_id} // 版本 v2 的查询用户列表的 API 接口
一、版本号:
命名版本号可以解决版本不兼容问题，在设计 RESTful API 的一种实用的做法是使用版本号。一般情况下，我们会在 url 中保留旧版本号，并同时兼容多个版本
【GET】 /api/pc/v1/users/{user_id} // 版本 v1 的查询用户列表的 API 接口
【GET】 /api/pc/v2/users/{user_id} // 版本 v2 的查询用户列表的 API 接口
/版本号/资源路径

/api/pc/v1/tags/{tag_id}

/api/pc/v1/users?[&keyword=xxx][&enable=1][&offset=0][&limit=20]
二、资源路径:
注意：只运行使用GET和POST方法，禁止使用PUT、DELETE、PATCH方法
1）资源的路径应该从根到子依次如下:
/{resources}/{resource_id}/{sub_resources}/{sub_resource_id}/{sub_resource_property}
【POST】 /api/pc/v1/users/{user_id}/roles/{role_id} // 添加用户的角色
2）当一个资源变化难以使用标准的 RESTful API 来命名，可以考虑使用一些特殊的 actions（add、delete、find、update） 命名。
/{resources}/{resource_id}/{action}
【POST】 /api/pc/v1/users/{user_id}/password/update// 密码修改
三、请求方法 :
使用HTTP方法（POST , GET）来代表资源的增删改查，并使用HTTP状态码来代表不同的结果。
方法
示例
描述
GET
/users/{user_id}/{user_name}
查询用户信息列表;针对查询请求参数个数小于等于5个用get请求；
GET
/users/1001
查看某个用户信息
POST

/users/find
查询用户信息列表;针对查询请求参数个数超过5个用post请求；
POST
/users/1001/delete
删除某个用户
POST
/users/1001/update
修改某个用户信息，
四、接口要求：
- 接口需要做认证。
- 接口需要无状态，不能依赖于会话（session）
五、统一前缀：
        推荐接口都以/api开头，便于统一处理。
六、分页及排序：
- 在URL中增加pageSize和pageNum参数用于分页。
- 在URL中增加sort参数用于排序，默认正序，带-表示倒序。比如：/api/pc/v1/users?sort=-create_user,create_time 代表create_user倒序，create_time正序。
- 返回的分页数据中，在响应全中，包含如下元素：
  pageSize(和请求值一样)
  pageNum(和请求值一样)
  total（能查出的总记录数）,注：total非必须，可以不传，以提高性能
  data（此次分页的数据）
七、请求体(Request body)：
- 文件上传采用multipart/form-data格式。
- 其它请求均采用application/json格式，不得使用application/x-www-form-urlencoded
八、响应状态码（Response status）：
如无必要，勿增实体。凡是HTTP Response Status能表述的，均使用HTTP的响应状态码。自定义状态码请参考前后端规范。
- 2XX
- 200 : 请求成功，响应体中带有资源的数据。
- 201 : 资源创建成功。
- 202 : 请求已接收。对于耗时较长的需要后台异步处理的，服务在接收请求后，返回202。
- 204 : 响应中无内容。通常是在执行PUT、POST、DELETE后，不返回资源的内容。
- 3XX
- 301 : 如果接口废弃，迁移到新接口中，可以返回301重定向。
- 304 : 资源未修改。和204一样，在响应体中不会带有内容，其区别在于如果针对请求头中的If-Modified-Since或者If-None-Match指定的版本，资源没有修改，则返回304。
- 4XX
- 400 ： 错误请求。通用的客户端错误状态码。通常是由于参数不正确。
- 401 ： 未授权。客户端未能提供有效的认证信息。
- 403 ： 请求被拒绝。客户端已经认证成功，但是无权访问该资源。
- 404 ： 资源不存在。
- 410 ： 如果接口永久废弃不用，可返回410。请慎用。
- 429 ： 请求次数超限。
- 5XX
- 500 ： 服务器内部错误
九、响应头（Response headers）：
        Content-Type需要与数据保持一致，POJO类的数据统一采用application/json;charset=UTF-8做Content-Type。
十、响应体（Response body）：
        除文件下载外，响应体均采用JSON格式，JSON不要进行格式化。针对4XX错误和5XX错误，需要提供有供的信息，格式如下：
{
    "code": 0,
    "data": *,
    "msg" : ""
}
十一、示例：
UserController.java

@RestController(/v1)

@API(tag=”用户相关接口”)

public class UserController {


    @Autowired

    private UserJPARepository userJPARepository;


    /**
     * 查询用户列表
     * @return
     */

    @GetMapping(value = "/user")

    public List<User> findUserList(){

        return userJPARepository.findAll();

    }


    /**
     * 根据Id查询一个用户
     * @param id
     * @return
     */

    @GetMapping(value = "/user/query/{id}")

    public User findUserOne(@PathVariable("id") Integer id){

        return userJPARepository.findOne(id);

    }


    /**
     * 添加用户
     * @param name
     * @param age
     * @param country
     * @return
     */


    @PostMapping(value = "/user")

    public User addUser(@RequestParam("name") String name, @RequestParam("age") int age,

                        @RequestParam("country") String country){

        User user = new User();

        user.setName(name);

        user.setAge(age);

        user.setCountry(country);

        return userJPARepository.save(user);

    }

    /**
     * 删除用户
     * @param id  用户编号
     * @return
     */

    @PostMapping(value = "/user/{id}/delete")

    public  List<User> deleteUser(@PathVariable("id") Integer id){

        userJPARepository.delete(id);

        return userJPARepository.findAll();

    }

    /**
     * 更新用户
     * @param id
     * @param name
     * @param age
     * @param country
     * @return
     */

    @PostMapping(value = "/user/{id}/update")

    public User updateUser(@PathVariable("id") Integer id, @RequestParam("name") String name,

                           @RequestParam("age") int age, @RequestParam("country") String country){

        User user = userJPARepository.findById(id);

        user.setName(name);

        user.setAge(age);

        user.setCountry(country);

        return userJPARepository.save(user);

    }

    /**
     * 根据国家查询用户
     * @param country
     * @return
     */

    @GetMapping(value = "/user/{country}")

    public List<User> findByCountry(@PathVariable("country") String country){

        return userJPARepository.findByCountry(country);

    }

}