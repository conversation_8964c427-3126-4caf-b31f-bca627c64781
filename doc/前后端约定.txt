一、 交互标准
1、对于静态HTML/XML页面的数据请求与返回
前端请求一个HTML/XML等文本格式页面用于渲染时，请统一使用GET请求。
文本页面编码除特殊情况外，一律采用UTF-8编码格式。
在后端返回的HTTP数据中，请确保Content-Type被设置并且为text/html,application/xhtml+xml或application/xml中的任意一个符合当前页面格式的值，并且正确显示的设置了编码，如下：
Content-Type:text/html;charset=UTF-8
2、对于静态资源的请求与返回
前端可以使用静态或动态添加方式，发起对一个或多个静态资源的请求。请遵循标准的的HTML标签规范确保携带正确的Accept头部信息。
后端根据指定的URI返回与前端正确的文件。如果出现未找到文件的情况，请按照HTTP标准返回404错误信息。
3、对于数据提交
前端提交数据，按情况可以使用表单提交或者Ajax传输方式。如果使用表单提交，请确保提交的Content-Type为application/x-www-form-urlencoded或multipart/form-data（用于伴随文件或者其他附加信息的提交）。
使用Ajax提交数据时，请确保预期的返回数据在HTTP请求头的Accept字段的首部。例如预期返回为json数据，则头部Accept字段可以如下：
Accept:application/json, */*;
4、请求中的参数
请遵守HTTP协议的参数传递并使用默认的参数传递分隔方式。在通常情况下，请不要在HTTP报文头部信息中放置自定义参数，例如在HTTP报文头部增加自定义的信息。
5、对于浏览器缓存
强烈建议在静态资源文件名或请求中加入唯一版本识别信息，信息可以为MD5标识，也可以为版本标识，以便在加载时浏览器判断是否使用缓存并在有更新时强制获取最新版本。
在Ajax请求中，对于动态加载数据时有可能会存在缓存残留问题(尤其在IE下)，因此建议在动态加载的发送请求时加入随机参数，例如：
http://.../constract/name/get?_=39487212321453

1. 【强制】前后端交互的 API，需要明确协议、域名、路径、请求方法、请求内容、状态码、响
应体。
　　说明：
　　1） 协议：生产环境必须使用 HTTPS。
　　2） 路径：每一个 API 需对应一个路径，表示 API 具体的请求地址：
a） 代表一种资源，只能为名词，推荐使用复数，不能为动词，请求方法已经表达动作意义。
b） URL 路径不能使用大写，单词如果需要分隔，统一使用下划线。
c） 路径禁止携带表示请求内容类型的后缀，比如".json",".xml"，通过 accept 头表达即可。
　　3） 请求方法：对具体操作的定义，常见的请求方法如下：
注意：只运行使用GET和POST方法，禁止使用PUT、DELETE方法
a） GET：从服务器取出资源。
b） POST：在服务器新建一个资源，针对查询请求参数超过5个用post请求；分页查询使用post请求。
　　4） 请求内容：URL 带的参数必须无敏感信息或符合安全要求；body 里带参数时必须设置 Content-Type。
　　5） 响应体：响应体 body 可放置多种数据类型，由 Content-Type 头来确定。
2. 【强制】前后端数据列表相关的接口返回，如果为空，则返回空数组[]或空集合{}。
　　说明：此条约定有利于数据层面上的协作更加高效，减少前端很多琐碎的 null 判断。

3. 【强制】服务端发生错误时，返回给前端的响应信息必须包含 HTTP 状态码，errorCode、
errorMessage、用户提示信息四个部分。
　　说明：四个部分的涉众对象分别是浏览器、前端开发、错误排查人员、用户。其中输出给用户的提示信息
　　要求：简短清晰、提示友好，引导用户进行下一步操作或解释错误原因，提示信息可以包括错误原因、上
下文环境、推荐操作等。 errorCode：参考附表 3。errorMessage：简要描述后端出错原因，便于错误排
查人员快速定位问题，注意不要包含敏感数据信息。
　　正例：常见的 HTTP 状态码如下
1） 200 OK: 表明该请求被成功地完成，所请求的资源发送到客户端。
2） 401 Unauthorized: 请求要求身份验证，常见对于需要登录而用户未登录的情况。
3） 403 Forbidden：服务器拒绝请求，常见于机密信息或复制其它登录用户链接访问服务器的情况。
4） 404 Not Found: 服务器无法取得所请求的网页，请求资源不存在。
5） 500 Internal Server Error: 服务器内部错误。
二、 交互数据格式
1、 通用返回数据规范
对于所有以JSON或者其他返回方式的数据，都需要有包含以下三个字段:
code: 整型。请求成功与否。0表示成功，其他表示失败。
data: 任意类型。所包含的数据
msg: 字符串型。如果有错误，则错误的信息放置在此，否则请设置为为空或null
JSON示例格式：
{
    "code": 0,
    "data": *,
    "msg" : ""
}
2、 分页信息的数据规范
对于分页信息的描述需要由以下4个字段定义：
currentPage：整型。当前的页码，与请求参数保持一致
pageSize：整型。当前每页的数量，与请求参数保持一致
totalCount：整型。总条目数
totalPage：整型。总页数
JSON示例格式：
{
    "currentPage": 0,
    "pageSize": 25,
    "totalCount": 8040,
    "totalPage": 322
}
3、 排序信息的数据规范
对于排序信息，需要由2个字段定义：
sortField：字符串型。排序的字段名称。
sortType：字符串类型。排序的类型，升序为”asc”，降序为”desc”
4、局部加载表格数据规范
(1)无分页信息
参数：按照业务所需的条件参数传递
返回：对于无分页的表格，在遵循Ajax JSON返回数据规范的基础上，将表单数据（通常为数组形式）放入data字段中。
JSON示例格式：
{
    "code": 0,
    "data": [{…},{…}],  //所包含的无分页的表格的列数据"msg" : ""
}
某些情况下，在一次请求中可能会出现多张表格数据返回的情况。对于此种情况，建议data变为一个map对象，并由key值区分多个数据。
JSON示例格式：
{
    "code": 0,
    "data": {
                "table1" : [{…},{…}], //表格1所包含的无分页的表格的列数据"table2" : [{…},{…}],                                        //表格2所包含的无分页的表格的列数据
                 …
            },
     "msg" : ""
}
(2)有分页信息
参数：除了按照业务所需的条件参数传递，还需要传递三个附加参数
page：当前请求的页码。页码从0开始，即第1页的请求页码为0
perPage：需要的每页的条目数。如果不指定，则按业务需求方的默认条目数。
skipIndex：当前已经跳过的条目。此参数为辅助条件，标识当前已经跳过的条目数量，例如当前请求页码(perPage)为1(第二页)，则该值为 1* perPage
返回：对于有分页的表格，在遵循Ajax JSON返回数据规范与分页信息的基础上，我们需要加入分页信息。本标准建议在分页信息同级别增加data字段用于包含数据。
JSON示例格式：
{
    "code": 0,
    "msg" : ""
    "data": {
                "data": [{…},{…}],
                "currentPage": 0,
                "pageSize": 25,
                "totalCount": 8040,
                "totalPage": 322
            }
    }
对于分页的，请不要在单个请求嵌入多个有分页信息的数据。
(3)有排序信息
参数：
当为单列排序时，除了按照业务所需的条件参数传递，还需要传递两个参数，sortField与sortType，此参数遵循排序循序的规范。
当为多列联合排序时，除了按照业务所需的条件参数传递，每个需要排序的条件需要增加一个排序描述参数，此参数名称以sort_为前缀并组合参数名，其值为排序的类型，升序为”asc”，降序为”desc”。
例如需要按照升序排序年龄(age)升序以及薪水(salary)降序，则参数为：
id: 0
name: “test”,
age: 30,
salary: 15000
sort_age: “asc”,
sort_salary: “desc”
三、 返回结果&状态码&数据字典
1、 返回结果
public class R<T> implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 成功 */
    public static final int SUCCESS = Constants.SUCCESS;

    /** 失败 */
    public static final int FAIL = Constants.FAIL;

    private int code;

    private String msg;

    private T data;

    public static <T> R<T> ok()
    {
        return restResult(null, SUCCESS, null);
    }

    public static <T> R<T> ok(T data)
    {
        return restResult(data, SUCCESS, null);
    }

    public static <T> R<T> ok(T data, String msg)
    {
        return restResult(data, SUCCESS, msg);
    }

    public static <T> R<T> fail()
    {
        return restResult(null, FAIL, null);
    }

    public static <T> R<T> fail(String msg)
    {
        return restResult(null, FAIL, msg);
    }

    public static <T> R<T> fail(T data)
    {
        return restResult(data, FAIL, null);
    }

    public static <T> R<T> fail(T data, String msg)
    {
        return restResult(data, FAIL, msg);
    }

    public static <T> R<T> fail(int code, String msg)
    {
        return restResult(null, code, msg);
    }

    public static <T> R<T> status(boolean flag) {
        return flag ? ok(null, "操作成功") : fail("操作失败");
    }

    private static <T> R<T> restResult(T data, int code, String msg)
    {
        R<T> apiResult = new R<>();
        apiResult.setCode(code);
        apiResult.setData(data);
        apiResult.setMsg(msg);
        return apiResult;
    }

    public int getCode()
    {
        return code;
    }

    public void setCode(int code)
    {
        this.code = code;
    }

    public String getMsg()
    {
        return msg;
    }

    public void setMsg(String msg)
    {
        this.msg = msg;
    }

    public T getData()
    {
        return data;
    }

    public void setData(T data)
    {
        this.data = data;
    }

    @Override
    public String toString() {
        return "R{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';
    }
}
2、 状态码
(1)用户交互状态码
序号
ENUM
code
msg
1
SUCCESS
1
请求成功
2
REGISTER
10001
注册成功
3
ALREADY_LOGIN
10002
已登录
4
NO_LOGIN
10003
未登录
5
NO_APPROVED
10004
未通过审核
6
APPROVED
10005
已通过审核
7
ACCOUNT_PWD_NOT_EXIST
10006
用户名或密码不存在
8
ACCOUNT_EXIST
10007
账号已存在
9
NO_PERMISSION
10008
无访问权限
(2)前后端状态码
序号
ENUM
code
msg
1
SESSION_TIME_OUT
50001
会话超时
2
TOKEN_ERROR
50002
token不合法
3
PARAMS_ERROR
50003
参数有误
4
PARAM_INCOMPLETE
50004
参数不完整
5
PARAM_ILLEGAL
50005
参数不合法
6
DATABASE_ERROR
50006
数据库连接异常
7
MONITOR_ERROR
50007
文件监控异常
8
SERVER_ERROR
50008
服务端错误
9
UNKNOWN_ERROR
50009
未知错误
10
NO_DATA
50010
无数据
11
DATA_EXISTS
50011
记录已存在
12
DETELE_ERROR
50012
删除错误
13
UPDATE_ERROR
50013
修改错误
14
SYS_ERROR
50014
系统错误，请重试
15
SERVICE_INVOKE_FAIL
50015
服务器调用失败
/* 状态码的值-封装到枚举类ErrorCode中 */
public enum ErrorCode {

    SESSION_TIME_OUT(50001,"会话超时"),
    TOKEN_ERROR(50002,"token不合法"),
    PARAMS_ERROR(50003,"参数有误");

    private int code;
    private String msg;

    ErrorCode(int code, String msg){
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}

3、 数据字典（码表返回格式）
1）码表返回格式
"label":"",
"value":""
2）主要有两张表：sys_dict_type 用于记录字典类型 ; sys_dict 用于记录字典数据（结合数据配置系统）
CREATE TABLE `sys_dict_type` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `NAME_EN` varchar(255) DEFAULT NULL COMMENT '英文名称',
  `NAME_CN` varchar(255) DEFAULT NULL COMMENT '中文名称',
  `STATUS` tinyint(4) DEFAULT '1',
  `SYSTEM_ID` tinyint(4) DEFAULT '1' COMMENT '1.卖家 2.买家  3.运营系统',
  `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8;


CREATE TABLE `sys_dict` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `LABEL` varchar(255) DEFAULT NULL COMMENT '字典数据名称',
  `DICT_TYPE_ID` bigint(20) DEFAULT NULL COMMENT '字典类型，与sys_dict_type关联',
  `VALUE` tinyint(4) DEFAULT NULL COMMENT '字典数据值',
  `SORT` int(255) DEFAULT NULL COMMENT '排序',
  `STATUS` tinyint(4) DEFAULT '1',
  `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8;
3）自定义字典工具类
package cn.yanxiaohui.dict;

import cn.yanxiaohui.vo.SysDict;

import java.util.ArrayList;
import java.util.List;


/**
 * 字典工具类
 * <AUTHOR>
 *
 */
public class DictUtil {

        /**
         * 通过字典类型的英文名称获取字典数据列表
         * @param enName 字典类型的英文名称
         * @return 字典数据列表
         */
        public static List<LabelValue> getDictsByEnName(String enName){
                List<SysDict> dicts=DictBean.getDicts();
                List<LabelValue> labelValues=new ArrayList<LabelValue>();
                for(SysDict dict:dicts){
                        if(enName.equals(dict.getNameEn())){
                                labelValues.add(new LabelValue(dict.getLabel(),dict.getValue().toString()));
                        }
                }
                return labelValues;
        }

        /**
         * 通过字典类型的英文名称获取字典数据名称
         * @param enName 字典类型的英文名称
         * @param value 字典数据的值
         * @return 字典数据名称
         */
        public static String getLabelByEnNameAndValue(String enName,Byte value){
                List<SysDict> dicts=DictBean.getDicts();
                for(SysDict dict:dicts){
                        if(dict.getNameEn().equals(enName) && value==dict.getValue()){
                                return dict.getLabel();
                        }
                }
                return "";
        }

        /**
         * 通过字典类型的中文名称获取字典数据名称
         * @param cnName 字典类型的中文名称
         * @param value 字典数据的值
         * @return 字典数据名称
         */
        public static String getLabelByCnNameAndValue(String cnName,Byte value){
                List<SysDict> dicts=DictBean.getDicts();
                for(SysDict dict:dicts){
                        if(dict.getNameEn().equals(cnName) && value==dict.getValue()){
                                return dict.getLabel();
                        }
                }
                return "";
        }

        /**
         * 通过字典类型的英文名和字典数据名称获取字典数据的值
         * @param enName 字典类型的英文名
         * @param label 字典数据名称
         * @return 字典数据的值
         */
        public static Byte getIdByEnNameAndLabel(String enName,String label){
                List<SysDict> dicts=DictBean.getDicts();
                for(SysDict dict:dicts){
                        if(label.equals(dict.getLabel()) && enName.equals(dict.getNameEn())){
                                return dict.getValue();
                        }
                }
                return null;
        }

        /**
         * 通过字典类型的中文名和字典数据名称获取字典数据的值
         * @param cnName 字典类型的中文名
         * @param label 字典数据名称
         * @return 字典数据的值
         */
        public static Byte getIdByCnNameAndLabel(String cnName,String label){
                List<SysDict> dicts=DictBean.getDicts();
                for(SysDict dict:dicts){
                        if(label.equals(dict.getLabel()) && cnName.equals(dict.getNameCn())){
                                return dict.getValue();
                        }
                }
                return null;
        }


}
其中LabelValue类是自定义用于替换以前枚举功能的视图层实体类
package cn.yanxiaohui.vo.dict;

import java.io.Serializable;

public class LabelValue implements Serializable {
        private static final long serialVersionUID = 3689355407466181430L;
        /**
         * The property which supplies the option label visible to the end user.
         */
        private String label = null;

        /**
         * The property which supplies the value returned to the server.
         */
        private String value = null;

        /**
         * Default constructor.
         */
        public LabelValue() {
                super();
        }

        /**
         * Construct an instance with the supplied property values.
         *
         * @param label
         *            The label to be displayed to the user.
         * @param value
         *            The value to be returned to the server.
         */
        public LabelValue(String label, String value) {
                this.label = label;
                this.value = value;
        }

        // ------------------------------------------------------------- Properties

        public String getLabel() {
                return this.label;
        }

        public void setLabel(String label) {
                this.label = label;
        }

        public String getValue() {
                return this.value;
        }

        public void setValue(String value) {
                this.value = value;
        }

        // --------------------------------------------------------- Public Methods

        /**
         * Compare LabelValueBeans based on the label, because that's the human
         * viewable part of the object.
         *
         * @see Comparable
         */
        public int compareTo(Object o) {
                // Implicitly tests for the correct type, throwing
                // ClassCastException as required by interface
                String otherLabel = ((LabelValue) o).getLabel();

                return this.getLabel().compareTo(otherLabel);
        }

        /**
         * Return a string representation of this object.
         */
        public String toString() {
                StringBuffer sb = new StringBuffer("LabelValue[");
                sb.append(this.label);
                sb.append(", ");
                sb.append(this.value);
                sb.append("]");
                return (sb.toString());
        }

        /**
         * LabelValueBeans are equal if their values are both null or equal.
         *
         * @see java.lang.Object#equals(java.lang.Object)
         */
        public boolean equals(Object obj) {
                if (obj == this) {
                        return true;
                }

                if (!(obj instanceof LabelValue)) {
                        return false;
                }

                LabelValue bean = (LabelValue) obj;
                int nil = (this.getValue() == null) ? 1 : 0;
                nil += (bean.getValue() == null) ? 1 : 0;

                if (nil == 2) {
                        return true;
                } else if (nil == 1) {
                        return false;
                } else {
                        return this.getValue().equals(bean.getValue());
                }

        }

        /**
         * The hash code is based on the object's value.
         *
         * @see java.lang.Object#hashCode()
         */
        public int hashCode() {
                return (this.getValue() == null) ? 17 : this.getValue().hashCode();
        }
}
5）定义用于视图数据获取的工具类（视图为velocity）

import org.apache.commons.lang3.StringUtils;

public class DictVelocity {

        public String label(String enName,Integer value) {
                String text="";
                if(StringUtils.isNotBlank(enName) && value != null){
                        text=DictUtil.getLabelByEnNameAndValue(enName,value.byteValue());
                }
                return text;
        }

        public String label(String enName,Byte value) {
                String text="";
                if(StringUtils.isNotBlank(enName) && value != null){
                        text=DictUtil.getLabelByEnNameAndValue(enName,value);
                }
                return text;
        }
}
四、 注意要点
1、 【强制】在前后端交互的 JSON 格式数据中，所有的 key 必须为小写字母开始的lowerCamelCase 风格，符合英文表达习惯，且表意完整。
　　正例：errorCode / errorMessage / assetStatus / menuList / orderList / configFlag
　　反例：ERRORCODE / ERROR_CODE / error_message / error-message / errormessage /
ErrorMessage / msg

2. 【强制】errorMessage 是前后端错误追踪机制的体现，可以在前端输出到 type="hidden"
文字类控件中，或者用户端的日志中，帮助我们快速地定位出问题。

3. 【强制】对于需要使用超大整数的场景，服务端一律使用 String 字符串类型返回，禁止使用
Long 类型。
　　说明：Java 服务端如果直接返回 Long 整型数据给前端，JS 会自动转换为 Number 类型（注：此类型为双
精度浮点数，表示原理与取值范围等同于 Java 中的 Double）。Long 类型能表示的最大值是 2 的 63 次方
-1，在取值范围之内，超过 2 的 53 次方 (9007199254740992)的数值转化为 JS 的 Number 时，有些数
值会有精度损失。扩展说明，在 Long 取值范围内，任何 2 的指数次整数都是绝对不会存在精度损失的，所
以说精度损失是一个概率问题。若浮点数尾数位与指数位空间不限，则可以精确表示任何整数，但很不幸，
双精度浮点数的尾数位只有 52 位。
　　反例：通常在订单号或交易号大于等于 16 位，大概率会出现前后端单据不一致的情况，比如，"orderId":
362909601374617692，前端拿到的值却是: 362909601374617660。

4. 【强制】HTTP 请求通过 URL 传递参数时，不能超过 2048 字节。
　　说明：不同浏览器对于 URL 的最大长度限制略有不同，并且对超出最大长度的处理逻辑也有差异，2048
字节是取所有浏览器的最小值。
　　反例：某业务将退货的商品 id 列表放在 URL 中作为参数传递，当一次退货商品数量过多时，URL 参数超长，
传递到后端的参数被截断，导致部分商品未能正确退货。

5. 【强制】HTTP 请求通过 body 传递内容时，必须控制长度，超出最大长度后，后端解析会出
错。
　　说明：nginx 默认限制是 1MB，tomcat 默认限制为 2MB，当确实有业务需要传较大内容时，可以通过调
大服务器端的限制。

6. 【强制】在翻页场景中，用户输入参数的小于 1，则前端返回第一页参数给后端；后端发现用
户输入的参数大于总页数，直接返回最后一页。

7. 【强制】服务器内部重定向必须使用 forward；外部重定向地址必须使用 URL 统一代理模块
生成，否则会因线上采用 HTTPS 协议而导致浏览器提示“不安全”，并且还会带来 URL 维护
不一致的问题。

【推荐】服务器返回信息必须被标记是否可以缓存，如果缓存，客户端可能会重用之前的请求
结果。
　　说明：缓存有利于减少交互次数，减少交互的平均延迟。
　　正例：http 1.1 中，s-maxage 告诉服务器进行缓存，时间单位为秒，用法如下，
response.setHeader("Cache-Control", "s-maxage=" + cacheSeconds);

8. 【推荐】服务端返回的数据，使用 JSON 格式而非 XML。
　　说明：尽管 HTTP 支持使用不同的输出格式，例如纯文本，JSON，CSV，XML，RSS 甚至 HTML。如果我
们使用的面向用户的服务，应该选择 JSON 作为通信中使用的标准数据交换格式，包括请求和响应。此外，
application/JSON 是一种通用的 MIME 类型，具有实用、精简、易读的特点。

9. 【推荐】前后端的时间格式统一为"yyyy-MM-dd HH:mm:ss"，统一为 GMT。

10.【参考】在接口路径中不要加入版本号，版本控制在 HTTP 头信息中体现，有利于向前兼容。
　　说明：当用户在低版本与高版本之间反复切换工作时，会导致迁移复杂度升高，存在数据错乱风险。