# 任务中心功能需求说明书

## 1. 需求概述

### 1.1 背景

为了更好地管理和跟踪储气库组织的月度采气计划和月度注气计划的上报工作，需要开发一个任务中心系统，实现计划任务的创建、分配、填报和跟踪的全流程管理。

### 1.2 目标

开发一个任务中心系统，实现以下目标：
1. 支持"计划上报"任务大类下的"月度采气计划"和"月度注气计划"两种任务小类的管理
2. 实现三级人员权限结构的任务分配和处理流程
3. 支持储气库及机构的动态配置和扩展
4. 提供任务状态的可视化展示和跟踪
5. 支持任务查询和筛选功能
6. 记录任务全生命周期的操作日志

### 1.3 范围限定

本次开发仅包含：
1. 任务大类：计划上报
2. 任务小类：月度采气计划、月度注气计划
3. 初始支持5个储气库及机构，并支持后续动态扩展
4. 考虑到后续兼容性，本次功能不涉及租户功能

## 2. 功能需求

### 2.1 用户角色与权限

#### 2.1.1 三级人员结构

1. **第一级（管理员）**
   - 权限：创建计划任务，分配给各库组织机构人员负责人
   - 可查看所有任务的状态和进度
   - 可对任务进行管理（修改、删除）

2. **第二级（各库组织机构负责人）**
   - 权限：接收管理员分配的计划填报任务
   - 可将任务指派给本组织机构下属成员
   - 在任务完成前可随时调整指派人员
   - 可查看本组织机构内所有任务的状态和进度

3. **第三级（各库组织机构成员）**
   - 权限：接收组织机构领导指派的填报任务
   - 编辑和填报任务内容
   - 提交填报结果
   - 只能查看分配给自己的任务

### 2.2 组织机构管理

#### 2.2.1 组织机构配置

1. **初始配置**
   - 支持初始5个储气库及机构的配置
   - 每个组织机构包含基本信息：ID、名称、类型、上级机构等
   - 基于cd_organization表存储组织机构信息

2. **动态扩展**
   - 支持后续动态添加新的储气库及机构
   - 支持组织机构的层级关系配置
   - 支持组织机构的启用/停用管理

#### 2.2.2 人员配置

1. **人员与组织关联**
   - 支持将人员关联到特定组织机构
   - 支持设置人员在组织中的角色（领导/成员）
   - 支持人员的调整和变更
   - 基于sys_user表存储用户信息

### 2.3 任务管理

#### 2.3.1 任务创建（管理员）

1. **基本信息**
   - 任务大类：固定为"计划上报"
   - 任务小类：选择"月度采气计划"或"月度注气计划"
   - 任务名称：如"2025年2月采气计划"
   - 计划年月：选择计划所属年月
   - 开始日期：任务开始日期
   - 结束日期：任务截止日期
   - 任务说明：可选填，提供任务的补充说明
   - 基于pm_base_task表存储任务基本信息

2. **任务分配**
   - 选择需要分配任务的机构，该机构范围配置到配置属性表中
   - 选择各机构的负责领导
   - 可批量创建并分配给多个机构
   - 基于pm_task_assignment表存储任务分配信息

#### 2.3.2 任务指派（组织机构负责人）

1. **接收任务**
   - 查看分配给自己所在组织的任务
   - 查看任务详情和要求

2. **指派任务**
   - 将任务指派给本组织下属成员
   - 可设置具体的填报要求和说明
   - 在任务完成前可随时调整指派人员
   - 通过更新pm_task_assignment表记录指派信息

#### 2.3.3 任务填报（组织机构成员）

1. **接收任务**
   - 查看被指派的填报任务
   - 查看任务详情和填报要求

2. **填报内容**
   - 根据任务类型（采气计划/注气计划）填写相应内容
   - 填报库机构可存在多个，具体填报库是该机构下属机构
   select * from cd_organization where belong_org_id =' 对应机构id' and org_class = '4';（）
   - 可保存草稿，存储在草稿表中
   - 完成后提交填报结果
   - 在提交后，数据才填报数据存储在op_ugs_inj_pro_plan_month表中

3. **修改填报**
   - 在截止日期前，未提交填报可修改草稿内容
   - 修改后需重新提交

#### 2.3.4 我的任务

1. **统计任务数量**
   - 我的代办（进行中、已超期）
   - 我的已办（已完成的任务）
   - 我参与的（我发起的或与我相关的）
   - 我创建的



### 2.4 任务状态管理

1. **状态定义**
   - 1：已完成,成员已提交填报内容状态变为已完成
   - 2：进行中，管理员创建任务后，状态为进行中，领导指派后只改处理人，状态不变
   - 3：已超期，任务未在规定时间内完成


2. **状态转换**
   - 管理员创建任务后，状态为"2：进行中"
   - 组织机构领导指派任务后，状态为"2：进行中"
   - 成员开始填报后，状态为"2：进行中"
   - 成员提交填报内容后，状态为"1：已完成"
   - 超过结束日期未完成自动转为"已超期"
### 2.4 任务流转日志记录

1. **查询任务日志记录，包含字段**
   - 时间
   - 操作（节点：下发，状态:已完成，说明，无）
   - 处理人
   - 耗时（小时）

### 2.5 月度采气计划和月度注气计划数据需求

#### 2.5.1 数据结构

基于op_ugs_inj_pro_plan_month表存储月度采气计划和月度注气计划数据，主要字段包括：

1. **基本信息字段**
   - inj_pro_issued_plan_id：储气库生产运行月计划ID（主键）
   - org_id：组织机构ID（关联cd_organization表）
   - prod_period：生产阶段，区分注气("INJ")或采气("PRO")
   - prod_date：生产日期，具体到天
   - prod_gas_volume：计划注/采气量（万方）

2. **管理字段**
   - bsflag：数据逻辑删除标识（1=在用，-5=废弃）
   - remarks：备注信息
   - create_date：创建时间
   - create_user_id：创建用户
   - update_date：更新时间
   - update_user_id：更新用户
   - check_date：审核时间
   - check_user_id：审核用户
   - tenant_id：租户ID

#### 2.5.2 月度采气计划填报需求

1. **填报方式**
   - 按时段填报：用户可以按时间段设置采气计划量
   - 系统自动将按时段填报的数据转换为具体日期数据存储
   - 支持批量填报：可以设置某一时间段内的统一计划量
   - 支持复制功能：可以复制历史计划数据作为基础进行修改
   - 填报回显： 计划表中该月如果存在已填报数据，需回显到填报内容中，按时段等方式

2. **数据项**
   - 生产日期：选择具体日期
   - 计划采气量：输入数值，单位为万方
   - 备注：可选填，说明特殊情况

3. **数据校验**
   - 采气量不能为负数
   - 采气量不能超过储气库最大日采气能力
   - 月度总采气量需符合储气库月度采气能力限制

4. **数据汇总**
   - 自动计算月度总采气量
   - 显示日均采气量
   - 生成采气量趋势图

#### 2.5.3 月度注气计划填报需求

1. **填报方式**
   - 按时段填报：用户可以按时间段设置注气计划量
   - 系统自动将按时段填报的数据转换为具体日期数据存储
   - 支持批量填报：可以设置某一时间段内的统一计划量
   - 支持复制功能：可以复制历史计划数据作为基础进行修改

2. **数据项**
   - 生产日期：选择具体日期
   - 计划注气量：输入数值，单位为万方
   - 备注：可选填，说明特殊情况

3. **数据校验**
   - 注气量不能为负数
   - 注气量不能超过储气库最大日注气能力
   - 月度总注气量需符合储气库月度注气能力限制

4. **数据汇总**
   - 自动计算月度总注气量
   - 显示日均注气量
   - 生成注气量趋势图

### 2.6 操作日志记录

#### 2.6.1 日志记录范围

记录任务全生命周期的操作日志，包括：

1. **任务创建日志**
   - 记录管理员创建任务的操作
   - 记录任务基本信息和分配信息

2. **任务流转日志**
   - 记录任务状态变更的操作
   - 记录任务指派的操作
   - 记录任务退回的操作

3. **任务操作日志**
   - 记录填报数据的操作
   - 记录修改数据的操作
   - 记录确认数据的操作

4. **任务完成日志**
   - 记录任务完成的操作
   - 记录任务完成时间和完成人

#### 2.6.2 日志内容

每条日志应包含以下信息：

1. **基本信息**
   - 日志ID：唯一标识
   - 操作类型：创建、指派、填报、保存草稿、提交等
   - 操作时间：精确到秒
   - 操作人：用户ID和姓名

2. **关联信息**
   - 任务ID：关联的任务
   - 组织机构ID：关联的组织机构
   - 相关数据ID：如填报数据ID

3. **操作详情**
   - 操作前状态：操作前的状态或数据
   - 操作后状态：操作后的状态或数据
   - 操作说明：操作的详细描述

### 2.7 任务提醒功能

#### 2.7.1 提醒类型

1. **任务发布提醒**
   - 当新任务被发布时，向负责人发送提醒
   - 提醒内容："负责人（您有X个新任务待完成，请前往"智能云平台-任务中心-我的任务"查看）"

2. **任务到期提醒**
   - 当任务即将到期时（当日/每日任务9点），向负责人发送提醒
   - 提醒内容："发起人+填报人（您有X个新任务已超期，请前往"智能云平台-任务中心-我的任务"查看）"

3. **任务超期提醒**
   - 当任务已经超期时（超过任务期后每日9点），向负责人发送提醒
   - 提醒内容："发起人+填报人（您有X个新任务已超期，请前往"智能云平台-任务中心-我的任务"查看）"

4. **任务删除提醒**
   - 当任务被删除时，向任务相关人员发送提醒
   - 提醒内容："删除后，消息提醒任务相关人员，您参与的【XX】任务已被发起人删除"

5. **任务信息修改提醒**
   - 当任务信息被修改时，向任务相关人员发送提醒
   - 提醒内容："修改后，消息提醒任务相关人员，您参与的【XX】任务已被发起人删除，请前往查看。"

#### 2.7.2 提醒方式

1. **系统内提醒**
   - 在系统内显示提醒消息
   - 提醒消息可以点击直接跳转到相应任务

2. **消息中心集成**
   - 提醒消息同时发送到用户的消息中心
   - 用户可以在消息中心查看历史提醒

#### 2.7.3 提醒配置

1. **提醒时间配置**
   - 任务到期提醒：当日9点
   - 任务超期提醒：超期后每日9点

2. **提醒对象配置**
   - 根据任务类型和提醒类型，自动确定提醒对象
   - 任务发布提醒：发送给负责人
   - 任务到期/超期提醒：发送给发起人和填报人
   - 任务删除/修改提醒：发送给所有任务相关人员

### 2.8 任务查询与筛选

1. **查询条件**
   - 组织机构：选择特定的储气库及机构
   - 任务小类：月度采气计划/月度注气计划
   - 计划年月：选择特定年月
   - 任务状态：选择特定状态
   - 负责人：按负责人筛选
   - 时间范围：按任务开始/结束日期筛选

2. **查询结果**
   - 显示符合条件的任务列表
   - 包含任务基本信息和当前状态
   - 支持排序和分页

## 3. 用户界面需求

### 3.1 任务中心主页

1. **顶部导航区**
   - 显示"我的任务"标题
   - 提供任务分类标签：我的待办、我的已办、我参与的、我创建的
   - 每个分类标签显示对应的任务数量

2. **查询筛选区**
   - 组织机构筛选：下拉选择组织机构
   - 任务小类筛选：选择月度采气计划/月度注气计划
   - 计划年月筛选：选择年月
   - 状态筛选：选择任务状态
   - 负责人筛选：输入负责人姓名
   - 时间范围筛选：选择开始和结束日期
   - 查询和重置按钮

3. **操作按钮区**
   - 新增按钮（仅管理员可见）：创建新任务
   - 修改按钮（仅管理员可见）：修改选中的任务
   - 删除按钮（仅管理员可见）：删除选中的任务

4. **任务列表区**
   - 显示任务列表，包含以下字段：
     - 序号
     - 任务小类（月度采气计划/月度注气计划）
     - 任务名称
     - 计划年月
     - 发起人
     - 责任部门
     - 负责人
     - 开始日期
     - 结束日期
     - 进展状态
     - 处理操作
   - 支持分页显示
   - 任务状态以不同颜色区分

### 3.2 任务创建/编辑页面

1. **基本信息区**
   - 任务大类：固定为"计划上报"
   - 任务小类：选择"月度采气计划"或"月度注气计划"
   - 任务名称：输入框
   - 计划年月：年月选择器
   - 开始日期：日期选择器
   - 结束日期：日期选择器
   - 任务说明：多行文本框

2. **任务分配区**
   - 组织机构选择：可多选
   - 负责领导选择：根据选择的组织机构显示可选领导
   - 批量分配按钮

3. **操作按钮区**
   - 保存按钮
   - 取消按钮

### 3.3 任务指派页面

1. **任务信息区**
   - 显示任务基本信息
   - 显示任务要求和说明

2. **指派区**
   - 成员选择：选择本组织下属成员
   - 指派说明：输入指派说明

3. **操作按钮区**
   - 指派按钮
   - 取消按钮

### 3.4 任务填报页面

1. **任务信息区**
   - 显示任务基本信息
   - 显示指派说明

2. **填报区**
   - 根据任务小类（月度采气计划/月度注气计划）显示对应的填报表单
   - 时间段选择：选择要填报的时间段
   - 数据输入：输入该时段的计划注/采气量
   - 数据汇总：显示月度总量和日均量
   - 趋势图：显示计划量趋势图

3. **操作按钮区**
   - 保存草稿按钮
   - 提交按钮
   - 取消按钮

### 3.5 任务确认页面

1. **任务信息区**
   - 显示任务基本信息
   - 显示填报人信息

2. **填报内容区**
   - 显示成员提交的填报内容
   - 数据汇总：显示月度总量和日均量
   - 趋势图：显示计划量趋势图

3. **确认意见区**
   - 确认意见输入框

4. **操作按钮区**
   - 退回修改按钮
   - 确认通过按钮
   - 取消按钮

## 4. 非功能需求

### 4.1 性能需求

1. 页面加载时间不超过3秒
2. 任务列表查询响应时间不超过2秒
3. 系统能够支持至少100个并发用户

### 4.2 安全需求

1. 用户只能查看和操作与自己权限相符的任务
2. 任务创建、指派和提交等操作需要记录操作日志
3. 敏感操作（如删除任务）需要权限控制

### 4.3 可扩展性需求

1. 系统架构需支持后续扩展更多任务大类和小类
2. 支持储气库及机构的动态添加和配置，当前支持5个储气库，后续可扩展
3. 支持用户角色和权限的灵活配置

### 4.4 兼容性需求

1. 支持主流浏览器（Chrome、Firefox、Edge）
2. 支持PC端访问

## 5. 数据需求

### 5.1 数据表设计

系统主要使用以下数据表：

1. **cd_organization**：组织机构表，存储储气库及机构信息
2. **sys_user**：用户表，存储用户基本信息
3. **pm_base_task**：任务基本信息表，存储任务的基本信息
4. **pm_task_assignment**：任务分配表，存储任务的分配信息
5. **op_ugs_inj_pro_plan_month**：储气库生产运行月计划表，存储月度采气计划和月度注气计划数据
6. **op_ugs_inj_pro_plan_draft**：储气库生产运行计划草稿表，存储用户填报的草稿数据
7. **pm_task_log**：任务日志表，记录任务全生命周期的操作日志

### 5.2 数据流转

1. 管理员创建任务，生成pm_base_task记录
2. 管理员分配任务，生成pm_task_assignment记录
3. 组织机构领导指派任务，更新pm_task_assignment记录
4. 组织机构成员填报数据，保存草稿时生成op_ugs_inj_pro_plan_draft记录
5. 组织机构成员提交填报结果，系统将草稿数据转存到op_ugs_inj_pro_plan_month表中，并更新pm_task_assignment记录的状态
6. 组织机构领导确认任务，更新pm_base_task和pm_task_assignment记录的状态
7. 每个操作环节生成pm_task_log记录，记录操作日志

### 5.3 数据备份与恢复

1. 系统数据需要定期备份
2. 提供数据恢复机制，确保数据安全

## 6. 实施计划

### 6.1 开发阶段

1. 需求分析与设计：3个工作日
2. 数据库设计与实现：2个工作日
3. 后端开发：5个工作日
4. 前端开发：5个工作日
5. 系统测试：3个工作日
6. 系统部署与上线：2个工作日

### 6.2 测试计划

1. 单元测试：确保各功能模块正常工作
2. 集成测试：确保系统各部分协同工作
3. 性能测试：验证系统在预期负载下的性能
4. 用户验收测试：确保系统满足用户需求

## 7. 附录

### 7.1 术语表

- 计划上报：任务大类，包含月度采气计划和月度注气计划
- 月度采气计划：每月需要填报的采气计划
- 月度注气计划：每月需要填报的注气计划
- 储气库：天然气储存设施
- 组织机构：储气库下设的各级组织单位

### 7.2 相关文档

- 组织机构管理规定
- 计划上报管理流程规范
- 月度采气计划填报规范
- 月度注气计划填报规范
