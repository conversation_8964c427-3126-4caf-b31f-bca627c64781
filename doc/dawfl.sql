/*
 Navicat Premium Data Transfer

 Source Server         : local_pg
 Source Server Type    : PostgreSQL
 Source Server Version : 140017
 Source Host           : localhost:1086
 Source Catalog        : CRP
 Source Schema         : dawfl

 Target Server Type    : PostgreSQL
 Target Server Version : 140017
 File Encoding         : 65001

 Date: 14/05/2025 15:23:07
*/


-- ----------------------------
-- Table structure for cd_attribute
-- ----------------------------
DROP TABLE IF EXISTS "dawfl"."cd_attribute";
CREATE TABLE "dawfl"."cd_attribute" (
  "attr_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "domain_id" varchar(32) COLLATE "pg_catalog"."default",
  "name" varchar(64) COLLATE "pg_catalog"."default",
  "code" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "coding_frame" varchar(32) COLLATE "pg_catalog"."default",
  "std_source" varchar(32) COLLATE "pg_catalog"."default",
  "specialty" varchar(24) COLLATE "pg_catalog"."default",
  "remarks" varchar(2000) COLLATE "pg_catalog"."default",
  "create_date" timestamp(6) NOT NULL,
  "create_user_id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "create_app_id" varchar(64) COLLATE "pg_catalog"."default",
  "update_date" timestamp(6) NOT NULL,
  "update_user_id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "check_date" timestamp(6),
  "check_user_id" varchar(64) COLLATE "pg_catalog"."default",
  "coding" varchar(32) COLLATE "pg_catalog"."default",
  "send_indicate" varchar(32) COLLATE "pg_catalog"."default",
  "data_source" varchar(512) COLLATE "pg_catalog"."default",
  "end_frame" varchar(20) COLLATE "pg_catalog"."default",
  "data_region" varchar(2) COLLATE "pg_catalog"."default",
  "source_data_id" varchar(256) COLLATE "pg_catalog"."default",
  "tenant_id" varchar(32) COLLATE "pg_catalog"."default",
  "bsflag" numeric(1,0) NOT NULL
)
;
COMMENT ON COLUMN "dawfl"."cd_attribute"."attr_id" IS '属性ID';
COMMENT ON COLUMN "dawfl"."cd_attribute"."domain_id" IS '域ID';
COMMENT ON COLUMN "dawfl"."cd_attribute"."name" IS '属性名称';
COMMENT ON COLUMN "dawfl"."cd_attribute"."code" IS '属性代码';
COMMENT ON COLUMN "dawfl"."cd_attribute"."coding_frame" IS '编码结构';
COMMENT ON COLUMN "dawfl"."cd_attribute"."std_source" IS '标准来源';
COMMENT ON COLUMN "dawfl"."cd_attribute"."specialty" IS '专业';
COMMENT ON COLUMN "dawfl"."cd_attribute"."remarks" IS '需要补充说明的内容';
COMMENT ON COLUMN "dawfl"."cd_attribute"."create_date" IS '记录数据在本系统的创建时间，需精确到时分秒';
COMMENT ON COLUMN "dawfl"."cd_attribute"."create_user_id" IS '记录数据在本系统的创建用户';
COMMENT ON COLUMN "dawfl"."cd_attribute"."create_app_id" IS '填写数据来源的系统名';
COMMENT ON COLUMN "dawfl"."cd_attribute"."update_date" IS '记录数据在本系统最新的更新时间，需精确到时分秒，默认=创建时间';
COMMENT ON COLUMN "dawfl"."cd_attribute"."update_user_id" IS '记录数据在本系统最新的更新用户，默认=创建用户';
COMMENT ON COLUMN "dawfl"."cd_attribute"."check_date" IS '记录数据在本系统的审核时间，需精确到时分秒';
COMMENT ON COLUMN "dawfl"."cd_attribute"."check_user_id" IS '记录数据在本系统的审核用户';
COMMENT ON COLUMN "dawfl"."cd_attribute"."coding" IS '填写对属性的编码。';
COMMENT ON COLUMN "dawfl"."cd_attribute"."send_indicate" IS '送审标识';
COMMENT ON COLUMN "dawfl"."cd_attribute"."data_source" IS '数据来源';
COMMENT ON COLUMN "dawfl"."cd_attribute"."end_frame" IS '编码未节点名称。填写：1：大类、2：亚类、3：子类、4：细类。';
COMMENT ON COLUMN "dawfl"."cd_attribute"."data_region" IS '油田标识';
COMMENT ON COLUMN "dawfl"."cd_attribute"."source_data_id" IS '存储数据来源的主键信息';
COMMENT ON COLUMN "dawfl"."cd_attribute"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "dawfl"."cd_attribute"."bsflag" IS '填写数据逻辑删除标识，1=在用，-5=废弃';
COMMENT ON TABLE "dawfl"."cd_attribute" IS '属性表';

-- ----------------------------
-- Table structure for cd_attribute_specs_value
-- ----------------------------
DROP TABLE IF EXISTS "dawfl"."cd_attribute_specs_value";
CREATE TABLE "dawfl"."cd_attribute_specs_value" (
  "value_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "parent_id" varchar(32) COLLATE "pg_catalog"."default",
  "attr_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "code" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "coding_mnemonic_id" varchar(32) COLLATE "pg_catalog"."default",
  "coding_abbreviation" varchar(100) COLLATE "pg_catalog"."default",
  "code_show_id" varchar(5) COLLATE "pg_catalog"."default",
  "value_level" varchar(10) COLLATE "pg_catalog"."default",
  "std_source" varchar(32) COLLATE "pg_catalog"."default",
  "remarks" varchar(2000) COLLATE "pg_catalog"."default",
  "create_date" timestamp(6) NOT NULL,
  "create_user_id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "create_app_id" varchar(64) COLLATE "pg_catalog"."default",
  "update_date" timestamp(6) NOT NULL,
  "update_user_id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "check_date" timestamp(6),
  "check_user_id" varchar(64) COLLATE "pg_catalog"."default",
  "parent_code" varchar(32) COLLATE "pg_catalog"."default",
  "send_indicate" varchar(32) COLLATE "pg_catalog"."default",
  "data_source" varchar(512) COLLATE "pg_catalog"."default",
  "end_if" varchar(1) COLLATE "pg_catalog"."default",
  "code_afford_org_id" varchar(32) COLLATE "pg_catalog"."default",
  "label" varchar(512) COLLATE "pg_catalog"."default",
  "data_region" varchar(2) COLLATE "pg_catalog"."default",
  "source_data_id" varchar(256) COLLATE "pg_catalog"."default",
  "tenant_id" varchar(32) COLLATE "pg_catalog"."default",
  "bsflag" numeric(1,0) NOT NULL
)
;
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."value_id" IS '数据表唯一标示符';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."parent_id" IS '上一级ID';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."attr_id" IS '属性表的唯一标示符';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."name" IS '属性值名称';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."code" IS '属性值代码';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."coding_mnemonic_id" IS '编码助记码，用于快捷键输入等场景';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."coding_abbreviation" IS '编码简称';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."code_show_id" IS '编码显示序号';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."value_level" IS '描述属性值层次，填写：大类、亚类、子类、细类';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."std_source" IS '标准来源';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."remarks" IS '需要补充说明的内容';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."create_date" IS '记录数据在本系统的创建时间，需精确到时分秒
';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."create_user_id" IS '记录数据在本系统的创建用户';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."create_app_id" IS '填写数据来源';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."update_date" IS '记录数据在本系统最新的更新时间，需精确到时分秒，默认=创建时间
';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."update_user_id" IS '记录数据在本系统最新的更新用户，默认=创建用户';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."check_date" IS '记录数据在本系统的审核时间，需精确到时分秒';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."check_user_id" IS '记录数据在本系统的审核用户';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."parent_code" IS '属性值的父代码，存储上一级属性值的编码。';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."send_indicate" IS '送审标识';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."data_source" IS '数据来源';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."end_if" IS '是否末级';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."code_afford_org_id" IS '编码使用的组织ID，公开时不填';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."label" IS '用于属性规范值分类的标签';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."data_region" IS '油田标识';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."source_data_id" IS '存储数据来源的主键信息';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "dawfl"."cd_attribute_specs_value"."bsflag" IS '填写数据逻辑删除标识，1=在用，-5=废弃';
COMMENT ON TABLE "dawfl"."cd_attribute_specs_value" IS '属性规范值';

-- ----------------------------
-- Table structure for cd_organization
-- ----------------------------
DROP TABLE IF EXISTS "dawfl"."cd_organization";
CREATE TABLE "dawfl"."cd_organization" (
  "org_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "org_name" varchar(256) COLLATE "pg_catalog"."default",
  "en_org_name" varchar(50) COLLATE "pg_catalog"."default",
  "local_org_name" varchar(50) COLLATE "pg_catalog"."default",
  "org_short_name" varchar(64) COLLATE "pg_catalog"."default",
  "corporate_representative" varchar(64) COLLATE "pg_catalog"."default",
  "org_desc" varchar(2000) COLLATE "pg_catalog"."default",
  "org_code" varchar(20) COLLATE "pg_catalog"."default",
  "org_type" varchar(32) COLLATE "pg_catalog"."default",
  "org_function" varchar(32) COLLATE "pg_catalog"."default",
  "org_level" varchar(32) COLLATE "pg_catalog"."default",
  "org_adm_level" varchar(32) COLLATE "pg_catalog"."default",
  "canton" varchar(64) COLLATE "pg_catalog"."default",
  "canton_code" varchar(32) COLLATE "pg_catalog"."default",
  "address" varchar(256) COLLATE "pg_catalog"."default",
  "home_url" varchar(64) COLLATE "pg_catalog"."default",
  "authorized_org_if" char(1) COLLATE "pg_catalog"."default",
  "forbid_if" char(1) COLLATE "pg_catalog"."default",
  "is_empty_org" char(1) COLLATE "pg_catalog"."default",
  "in_use" varchar(2) COLLATE "pg_catalog"."default",
  "remarks" varchar(2000) COLLATE "pg_catalog"."default",
  "create_date" timestamp(6) NOT NULL,
  "create_user_id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "create_app_id" varchar(64) COLLATE "pg_catalog"."default",
  "update_date" timestamp(6),
  "update_user_id" varchar(64) COLLATE "pg_catalog"."default",
  "check_date" timestamp(6),
  "check_user_id" varchar(64) COLLATE "pg_catalog"."default",
  "mdm_code" varchar(32) COLLATE "pg_catalog"."default",
  "canton_desc" varchar(256) COLLATE "pg_catalog"."default",
  "company_id" varchar(32) COLLATE "pg_catalog"."default",
  "customer_division" varchar(64) COLLATE "pg_catalog"."default",
  "customer_group" varchar(64) COLLATE "pg_catalog"."default",
  "customer_telephone" varchar(64) COLLATE "pg_catalog"."default",
  "reporting_standard" numeric(5,0),
  "reporting_time" timestamp(6),
  "attachment_journal_id" varchar(5) COLLATE "pg_catalog"."default",
  "must_verify_flag" varchar(1) COLLATE "pg_catalog"."default",
  "oil_field" varchar(50) COLLATE "pg_catalog"."default",
  "send_indicate" varchar(32) COLLATE "pg_catalog"."default",
  "data_source" varchar(512) COLLATE "pg_catalog"."default",
  "sort_seq" varchar(10) COLLATE "pg_catalog"."default",
  "construct_start" timestamp(6),
  "construct_end" timestamp(6),
  "project_dept_id" varchar(32) COLLATE "pg_catalog"."default",
  "tenant_id" varchar(32) COLLATE "pg_catalog"."default",
  "data_region" varchar(2) COLLATE "pg_catalog"."default",
  "source_data_id" varchar(256) COLLATE "pg_catalog"."default",
  "org_class" varchar(10) COLLATE "pg_catalog"."default",
  "belong_org_id" varchar(40) COLLATE "pg_catalog"."default",
  "bsflag" numeric(1,0) NOT NULL,
  "ent_code" varchar(20) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "dawfl"."cd_organization"."org_id" IS '机构ID';
COMMENT ON COLUMN "dawfl"."cd_organization"."org_name" IS '机构名称';
COMMENT ON COLUMN "dawfl"."cd_organization"."en_org_name" IS '英文名称';
COMMENT ON COLUMN "dawfl"."cd_organization"."local_org_name" IS '本地名称';
COMMENT ON COLUMN "dawfl"."cd_organization"."org_short_name" IS '机构简称';
COMMENT ON COLUMN "dawfl"."cd_organization"."corporate_representative" IS '法人代表';
COMMENT ON COLUMN "dawfl"."cd_organization"."org_desc" IS '组织机构简介';
COMMENT ON COLUMN "dawfl"."cd_organization"."org_code" IS '机构编码';
COMMENT ON COLUMN "dawfl"."cd_organization"."org_type" IS '机构类型，引用属性代码ORG_TYPE下的属性值';
COMMENT ON COLUMN "dawfl"."cd_organization"."org_function" IS '机构职能';
COMMENT ON COLUMN "dawfl"."cd_organization"."org_level" IS '机构级别,引用属性代码ORG_LEVEL下的属性值';
COMMENT ON COLUMN "dawfl"."cd_organization"."org_adm_level" IS '机构行政级别，引用属性代码ORG_ADMIN_LEVEL下的属性值';
COMMENT ON COLUMN "dawfl"."cd_organization"."canton" IS '行政区名称，填写行政区代码对应的行政区名称';
COMMENT ON COLUMN "dawfl"."cd_organization"."canton_code" IS '行政区代码，引用属性代码CANTON下的属性值';
COMMENT ON COLUMN "dawfl"."cd_organization"."address" IS '邮寄地址';
COMMENT ON COLUMN "dawfl"."cd_organization"."home_url" IS '主页url地址';
COMMENT ON COLUMN "dawfl"."cd_organization"."authorized_org_if" IS '是否在编组织机构';
COMMENT ON COLUMN "dawfl"."cd_organization"."forbid_if" IS '生产单位标识 是:Y 否:N';
COMMENT ON COLUMN "dawfl"."cd_organization"."is_empty_org" IS '是否虚机构';
COMMENT ON COLUMN "dawfl"."cd_organization"."in_use" IS '在用状态';
COMMENT ON COLUMN "dawfl"."cd_organization"."remarks" IS '备注';
COMMENT ON COLUMN "dawfl"."cd_organization"."create_date" IS '记录数据在本系统的创建时间，需精确到时分秒';
COMMENT ON COLUMN "dawfl"."cd_organization"."create_user_id" IS '记录数据在本系统的创建用户';
COMMENT ON COLUMN "dawfl"."cd_organization"."create_app_id" IS '填写数据来源的系统名';
COMMENT ON COLUMN "dawfl"."cd_organization"."update_date" IS '记录数据在本系统最新的更新时间，需精确到时分秒，默认=创建时间';
COMMENT ON COLUMN "dawfl"."cd_organization"."update_user_id" IS '记录数据在本系统最新的更新用户，默认=创建用户';
COMMENT ON COLUMN "dawfl"."cd_organization"."check_date" IS '记录数据在本系统的审核时间，需精确到时分秒';
COMMENT ON COLUMN "dawfl"."cd_organization"."check_user_id" IS '记录数据在本系统的审核用户';
COMMENT ON COLUMN "dawfl"."cd_organization"."mdm_code" IS 'MDM编码';
COMMENT ON COLUMN "dawfl"."cd_organization"."canton_desc" IS '描述';
COMMENT ON COLUMN "dawfl"."cd_organization"."company_id" IS '公司代码';
COMMENT ON COLUMN "dawfl"."cd_organization"."customer_division" IS '部门';
COMMENT ON COLUMN "dawfl"."cd_organization"."customer_group" IS '小组';
COMMENT ON COLUMN "dawfl"."cd_organization"."customer_telephone" IS '电话';
COMMENT ON COLUMN "dawfl"."cd_organization"."reporting_standard" IS '报告标准';
COMMENT ON COLUMN "dawfl"."cd_organization"."reporting_time" IS '报告时间';
COMMENT ON COLUMN "dawfl"."cd_organization"."attachment_journal_id" IS '文档日志';
COMMENT ON COLUMN "dawfl"."cd_organization"."must_verify_flag" IS '必须审核标志';
COMMENT ON COLUMN "dawfl"."cd_organization"."oil_field" IS '油区';
COMMENT ON COLUMN "dawfl"."cd_organization"."send_indicate" IS '传输标识';
COMMENT ON COLUMN "dawfl"."cd_organization"."data_source" IS '填写数据来源的表CODE';
COMMENT ON COLUMN "dawfl"."cd_organization"."sort_seq" IS '排序编码';
COMMENT ON COLUMN "dawfl"."cd_organization"."construct_start" IS '建设开始日期';
COMMENT ON COLUMN "dawfl"."cd_organization"."construct_end" IS '建设结束日期';
COMMENT ON COLUMN "dawfl"."cd_organization"."project_dept_id" IS '项目部/作业区ID,对应组织机构ID';
COMMENT ON COLUMN "dawfl"."cd_organization"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "dawfl"."cd_organization"."data_region" IS '油田标识';
COMMENT ON COLUMN "dawfl"."cd_organization"."source_data_id" IS '存储数据来源的主键信息';
COMMENT ON COLUMN "dawfl"."cd_organization"."bsflag" IS '填写数据逻辑删除标识，1=在用，-5=废弃';
COMMENT ON COLUMN "dawfl"."cd_organization"."ent_code" IS '统一社会信用代码';
COMMENT ON TABLE "dawfl"."cd_organization" IS '组织机构';

-- ----------------------------
-- Table structure for op_ugs_inj_pro_plan_month
-- ----------------------------
DROP TABLE IF EXISTS "dawfl"."op_ugs_inj_pro_plan_month";
CREATE TABLE "dawfl"."op_ugs_inj_pro_plan_month" (
  "inj_pro_issued_plan_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "org_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "prod_period" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "prod_date" timestamp(6) NOT NULL,
  "prod_gas_volume" numeric(15,6),
  "bsflag" numeric(1,0) NOT NULL,
  "remarks" varchar(2000) COLLATE "pg_catalog"."default",
  "create_date" timestamp(6) NOT NULL,
  "create_user_id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "create_app_id" varchar(64) COLLATE "pg_catalog"."default",
  "update_date" timestamp(6) NOT NULL,
  "update_user_id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "check_date" timestamp(6),
  "check_user_id" varchar(64) COLLATE "pg_catalog"."default",
  "data_source" varchar(512) COLLATE "pg_catalog"."default",
  "source_data_id" varchar(256) COLLATE "pg_catalog"."default",
  "data_region" varchar(2) COLLATE "pg_catalog"."default",
  "tenant_id" varchar(36) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "dawfl"."op_ugs_inj_pro_plan_month"."inj_pro_issued_plan_id" IS '储气库生产运行月计划ID';
COMMENT ON COLUMN "dawfl"."op_ugs_inj_pro_plan_month"."org_id" IS '组织机构ID';
COMMENT ON COLUMN "dawfl"."op_ugs_inj_pro_plan_month"."prod_period" IS '生产阶段（注/采）';
COMMENT ON COLUMN "dawfl"."op_ugs_inj_pro_plan_month"."prod_date" IS '生产日期';
COMMENT ON COLUMN "dawfl"."op_ugs_inj_pro_plan_month"."prod_gas_volume" IS '计划注/采气量（万方）';
COMMENT ON COLUMN "dawfl"."op_ugs_inj_pro_plan_month"."bsflag" IS '填写数据逻辑删除标识，1=在用，-5=废弃';
COMMENT ON COLUMN "dawfl"."op_ugs_inj_pro_plan_month"."remarks" IS '需要补充说明的内容';
COMMENT ON COLUMN "dawfl"."op_ugs_inj_pro_plan_month"."create_date" IS '记录数据在本系统的创建时间，需精确到时分秒';
COMMENT ON COLUMN "dawfl"."op_ugs_inj_pro_plan_month"."create_user_id" IS '记录数据在本系统的创建用户';
COMMENT ON COLUMN "dawfl"."op_ugs_inj_pro_plan_month"."create_app_id" IS '填写数据来源的系统名';
COMMENT ON COLUMN "dawfl"."op_ugs_inj_pro_plan_month"."update_date" IS '记录数据在本系统最新的更新时间，需精确到时分秒，默认=创建时间';
COMMENT ON COLUMN "dawfl"."op_ugs_inj_pro_plan_month"."update_user_id" IS '记录数据在本系统最新的更新用户，默认=创建用户';
COMMENT ON COLUMN "dawfl"."op_ugs_inj_pro_plan_month"."check_date" IS '记录数据在本系统的审核时间，需精确到时分秒';
COMMENT ON COLUMN "dawfl"."op_ugs_inj_pro_plan_month"."check_user_id" IS '记录数据在本系统的审核用户';
COMMENT ON COLUMN "dawfl"."op_ugs_inj_pro_plan_month"."data_source" IS '填写数据来源的表CODE';
COMMENT ON COLUMN "dawfl"."op_ugs_inj_pro_plan_month"."source_data_id" IS '存储数据来源的主键信息';
COMMENT ON COLUMN "dawfl"."op_ugs_inj_pro_plan_month"."data_region" IS '油田标识';
COMMENT ON COLUMN "dawfl"."op_ugs_inj_pro_plan_month"."tenant_id" IS '租户ID';
COMMENT ON TABLE "dawfl"."op_ugs_inj_pro_plan_month" IS '储气库生产运行月计划';

-- ----------------------------
-- Table structure for pm_base_task
-- ----------------------------
DROP TABLE IF EXISTS "dawfl"."pm_base_task";
CREATE TABLE "dawfl"."pm_base_task" (
  "task_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "parent_task_id" varchar(32) COLLATE "pg_catalog"."default",
  "org_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "task_class" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "sub_task_class" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "task_name" varchar(128) COLLATE "pg_catalog"."default" NOT NULL,
  "task_desc" varchar(256) COLLATE "pg_catalog"."default",
  "start_time" timestamp(6) NOT NULL,
  "end_time" timestamp(6) NOT NULL,
  "sender" varchar(32) COLLATE "pg_catalog"."default",
  "send_date" timestamp(6),
  "is_supervised" varchar(1) COLLATE "pg_catalog"."default",
  "attachment_file_id" varchar(2000) COLLATE "pg_catalog"."default",
  "task_status" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
  "finish_time" timestamp(6),
  "bsflag" numeric(1,0) NOT NULL,
  "remarks" varchar(2000) COLLATE "pg_catalog"."default",
  "create_date" timestamp(6),
  "create_user_id" varchar(64) COLLATE "pg_catalog"."default",
  "create_app_id" varchar(64) COLLATE "pg_catalog"."default",
  "update_date" timestamp(6),
  "update_user_id" varchar(64) COLLATE "pg_catalog"."default",
  "check_date" timestamp(6),
  "check_user_id" varchar(64) COLLATE "pg_catalog"."default",
  "data_source" varchar(512) COLLATE "pg_catalog"."default",
  "source_data_id" varchar(256) COLLATE "pg_catalog"."default",
  "data_region" varchar(2) COLLATE "pg_catalog"."default",
  "tenant_id" varchar(32) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "dawfl"."pm_base_task"."task_id" IS '任务ID';
COMMENT ON COLUMN "dawfl"."pm_base_task"."parent_task_id" IS '父任务ID';
COMMENT ON COLUMN "dawfl"."pm_base_task"."org_id" IS '组织机构ID';
COMMENT ON COLUMN "dawfl"."pm_base_task"."task_class" IS '任务大类，参照CD_ATTRIBUTE中属性TASK_CLASS所取的值，大类';
COMMENT ON COLUMN "dawfl"."pm_base_task"."sub_task_class" IS '任务小类，参照CD_ATTRIBUTE中属性TASK_CLASS所取的值，亚类';
COMMENT ON COLUMN "dawfl"."pm_base_task"."task_name" IS '任务名称';
COMMENT ON COLUMN "dawfl"."pm_base_task"."task_desc" IS '任务描述';
COMMENT ON COLUMN "dawfl"."pm_base_task"."start_time" IS '要求开始日期';
COMMENT ON COLUMN "dawfl"."pm_base_task"."end_time" IS '要求结束日期';
COMMENT ON COLUMN "dawfl"."pm_base_task"."sender" IS '发布人';
COMMENT ON COLUMN "dawfl"."pm_base_task"."send_date" IS '发布时间';
COMMENT ON COLUMN "dawfl"."pm_base_task"."is_supervised" IS '是否多人协作，1是，0否';
COMMENT ON COLUMN "dawfl"."pm_base_task"."attachment_file_id" IS '附件（多个）';
COMMENT ON COLUMN "dawfl"."pm_base_task"."task_status" IS '任务状态，1已完成；2进行中；3已超期';
COMMENT ON COLUMN "dawfl"."pm_base_task"."finish_time" IS '任务完成时间';
COMMENT ON COLUMN "dawfl"."pm_base_task"."bsflag" IS '填写数据逻辑删除标识，1=在用，-5=废弃';
COMMENT ON COLUMN "dawfl"."pm_base_task"."remarks" IS '备注';
COMMENT ON COLUMN "dawfl"."pm_base_task"."create_date" IS '创建日期';
COMMENT ON COLUMN "dawfl"."pm_base_task"."create_user_id" IS '创建用户';
COMMENT ON COLUMN "dawfl"."pm_base_task"."create_app_id" IS '填写数据来源的系统名';
COMMENT ON COLUMN "dawfl"."pm_base_task"."update_date" IS '更新日期';
COMMENT ON COLUMN "dawfl"."pm_base_task"."update_user_id" IS '更新用户';
COMMENT ON COLUMN "dawfl"."pm_base_task"."check_date" IS '记录数据在本系统的审核时间，需精确到时分秒';
COMMENT ON COLUMN "dawfl"."pm_base_task"."check_user_id" IS '记录数据在本系统的审核用户';
COMMENT ON COLUMN "dawfl"."pm_base_task"."data_source" IS '填写数据来源的表CODE';
COMMENT ON COLUMN "dawfl"."pm_base_task"."source_data_id" IS '存储数据来源的主键信息';
COMMENT ON COLUMN "dawfl"."pm_base_task"."data_region" IS '油田标识';
COMMENT ON COLUMN "dawfl"."pm_base_task"."tenant_id" IS '租户id';
COMMENT ON TABLE "dawfl"."pm_base_task" IS '任务';

-- ----------------------------
-- Table structure for pm_task_assignment
-- ----------------------------
DROP TABLE IF EXISTS "dawfl"."pm_task_assignment";
CREATE TABLE "dawfl"."pm_task_assignment" (
  "task_assign_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "task_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "employee_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "task_status" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
  "finish_time" timestamp(6),
  "bsflag" numeric(1,0) NOT NULL,
  "remarks" varchar(2000) COLLATE "pg_catalog"."default",
  "create_date" timestamp(6),
  "create_user_id" varchar(64) COLLATE "pg_catalog"."default",
  "create_app_id" varchar(64) COLLATE "pg_catalog"."default",
  "update_date" timestamp(6),
  "update_user_id" varchar(64) COLLATE "pg_catalog"."default",
  "check_date" timestamp(6),
  "check_user_id" varchar(64) COLLATE "pg_catalog"."default",
  "data_source" varchar(512) COLLATE "pg_catalog"."default",
  "source_data_id" varchar(256) COLLATE "pg_catalog"."default",
  "data_region" varchar(2) COLLATE "pg_catalog"."default",
  "tenant_id" varchar(32) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "dawfl"."pm_task_assignment"."task_assign_id" IS '任务分配ID';
COMMENT ON COLUMN "dawfl"."pm_task_assignment"."task_id" IS '任务ID，外键关联：PM_BASE_TASK';
COMMENT ON COLUMN "dawfl"."pm_task_assignment"."employee_id" IS '人员ID，外键关联：CD_EMPLOYEE';
COMMENT ON COLUMN "dawfl"."pm_task_assignment"."task_status" IS '任务状态，1已完成；2进行中；3已超期';
COMMENT ON COLUMN "dawfl"."pm_task_assignment"."finish_time" IS '任务完成时间';
COMMENT ON COLUMN "dawfl"."pm_task_assignment"."bsflag" IS '填写数据逻辑删除标识，1=在用，-5=废弃';
COMMENT ON COLUMN "dawfl"."pm_task_assignment"."remarks" IS '备注';
COMMENT ON COLUMN "dawfl"."pm_task_assignment"."create_date" IS '创建日期';
COMMENT ON COLUMN "dawfl"."pm_task_assignment"."create_user_id" IS '创建用户';
COMMENT ON COLUMN "dawfl"."pm_task_assignment"."create_app_id" IS '填写数据来源的系统名';
COMMENT ON COLUMN "dawfl"."pm_task_assignment"."update_date" IS '更新日期';
COMMENT ON COLUMN "dawfl"."pm_task_assignment"."update_user_id" IS '更新用户';
COMMENT ON COLUMN "dawfl"."pm_task_assignment"."check_date" IS '记录数据在本系统的审核时间，需精确到时分秒';
COMMENT ON COLUMN "dawfl"."pm_task_assignment"."check_user_id" IS '记录数据在本系统的审核用户';
COMMENT ON COLUMN "dawfl"."pm_task_assignment"."data_source" IS '填写数据来源的表CODE';
COMMENT ON COLUMN "dawfl"."pm_task_assignment"."source_data_id" IS '存储数据来源的主键信息';
COMMENT ON COLUMN "dawfl"."pm_task_assignment"."data_region" IS '油田标识';
COMMENT ON COLUMN "dawfl"."pm_task_assignment"."tenant_id" IS '租户id';
COMMENT ON TABLE "dawfl"."pm_task_assignment" IS '任务分配表';

-- ----------------------------
-- Table structure for sys_tenant
-- ----------------------------
DROP TABLE IF EXISTS "dawfl"."sys_tenant";
CREATE TABLE "dawfl"."sys_tenant" (
  "tenant_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "tenant_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "tenant_code" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "start_date" timestamp(6),
  "end_date" timestamp(6),
  "status" varchar(1) COLLATE "pg_catalog"."default",
  "sort_no" numeric(3,0),
  "bsflag" numeric(1,0),
  "remarks" varchar(2000) COLLATE "pg_catalog"."default",
  "create_date" timestamp(6),
  "create_user_id" varchar(64) COLLATE "pg_catalog"."default",
  "create_app_id" varchar(64) COLLATE "pg_catalog"."default",
  "update_date" timestamp(6),
  "update_user_id" varchar(64) COLLATE "pg_catalog"."default",
  "check_date" timestamp(6),
  "check_user_id" varchar(64) COLLATE "pg_catalog"."default",
  "data_source" varchar(512) COLLATE "pg_catalog"."default",
  "source_data_id" varchar(256) COLLATE "pg_catalog"."default",
  "data_region" varchar(2) COLLATE "pg_catalog"."default",
  "create_user_name" varchar(50) COLLATE "pg_catalog"."default",
  "update_user_name" varchar(50) COLLATE "pg_catalog"."default",
  "tenant_org_id" varchar(32) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "dawfl"."sys_tenant"."tenant_id" IS '租户id';
COMMENT ON COLUMN "dawfl"."sys_tenant"."tenant_name" IS '租户名称';
COMMENT ON COLUMN "dawfl"."sys_tenant"."tenant_code" IS '租户编号';
COMMENT ON COLUMN "dawfl"."sys_tenant"."start_date" IS '开始时间';
COMMENT ON COLUMN "dawfl"."sys_tenant"."end_date" IS '结束时间';
COMMENT ON COLUMN "dawfl"."sys_tenant"."status" IS '租户状态 0正常 1-冻结';
COMMENT ON COLUMN "dawfl"."sys_tenant"."bsflag" IS '填写数据逻辑删除标识，1=在用，-5=废弃';
COMMENT ON COLUMN "dawfl"."sys_tenant"."remarks" IS '备注';
COMMENT ON COLUMN "dawfl"."sys_tenant"."create_date" IS '创建时间';
COMMENT ON COLUMN "dawfl"."sys_tenant"."create_user_id" IS '创建用户';
COMMENT ON COLUMN "dawfl"."sys_tenant"."create_app_id" IS '数据来源系统';
COMMENT ON COLUMN "dawfl"."sys_tenant"."update_date" IS '更新时间';
COMMENT ON COLUMN "dawfl"."sys_tenant"."update_user_id" IS '更新用户';
COMMENT ON COLUMN "dawfl"."sys_tenant"."check_date" IS '审核时间';
COMMENT ON COLUMN "dawfl"."sys_tenant"."check_user_id" IS '审核用户';
COMMENT ON COLUMN "dawfl"."sys_tenant"."data_source" IS '数据来源表CODE';
COMMENT ON COLUMN "dawfl"."sys_tenant"."source_data_id" IS '数据来源表主键';
COMMENT ON COLUMN "dawfl"."sys_tenant"."data_region" IS '油田标识';
COMMENT ON COLUMN "dawfl"."sys_tenant"."create_user_name" IS '创建用户名称';
COMMENT ON COLUMN "dawfl"."sys_tenant"."update_user_name" IS '更新用户名称';
COMMENT ON TABLE "dawfl"."sys_tenant" IS '租户表';

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS "dawfl"."sys_user";
CREATE TABLE "dawfl"."sys_user" (
  "user_id" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "login_name" varchar(20) COLLATE "pg_catalog"."default",
  "display_name" varchar(50) COLLATE "pg_catalog"."default",
  "avatar" varchar(200) COLLATE "pg_catalog"."default",
  "dept_id" varchar(200) COLLATE "pg_catalog"."default",
  "birthday" timestamp(6),
  "identity_card" varchar(20) COLLATE "pg_catalog"."default",
  "sex" varchar(2) COLLATE "pg_catalog"."default",
  "password" varchar(200) COLLATE "pg_catalog"."default",
  "user_type" varchar(20) COLLATE "pg_catalog"."default",
  "remarks" varchar(2000) COLLATE "pg_catalog"."default",
  "create_user_id" varchar(100) COLLATE "pg_catalog"."default",
  "create_date" timestamp(6),
  "update_user_id" varchar(100) COLLATE "pg_catalog"."default",
  "update_date" timestamp(6),
  "version" varchar(100) COLLATE "pg_catalog"."default",
  "extend_field" varchar(500) COLLATE "pg_catalog"."default",
  "email" varchar(100) COLLATE "pg_catalog"."default",
  "phone_number" varchar(100) COLLATE "pg_catalog"."default",
  "user_sort" numeric(38,0),
  "tenant_id" varchar(32) COLLATE "pg_catalog"."default",
  "status" varchar(1) COLLATE "pg_catalog"."default",
  "del_flag" varchar(1) COLLATE "pg_catalog"."default",
  "login_ip" varchar(128) COLLATE "pg_catalog"."default",
  "login_date" timestamp(6),
  "local_password" varchar(200) COLLATE "pg_catalog"."default",
  "display_order" varchar(10) COLLATE "pg_catalog"."default",
  "create_by" varchar(40) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_by" varchar(40) COLLATE "pg_catalog"."default",
  "update_time" timestamp(6),
  "code" varchar(20) COLLATE "pg_catalog"."default",
  "bsflag" numeric(1,0),
  "create_app_id" varchar(64) COLLATE "pg_catalog"."default",
  "check_date" timestamp(6),
  "check_user_id" varchar(64) COLLATE "pg_catalog"."default",
  "data_source" varchar(512) COLLATE "pg_catalog"."default",
  "source_data_id" varchar(256) COLLATE "pg_catalog"."default",
  "data_region" varchar(2) COLLATE "pg_catalog"."default",
  "employee_id" varchar(32) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "dawfl"."sys_user"."user_id" IS '用户唯一标识';
COMMENT ON COLUMN "dawfl"."sys_user"."login_name" IS '登录ID';
COMMENT ON COLUMN "dawfl"."sys_user"."display_name" IS '用户姓名';
COMMENT ON COLUMN "dawfl"."sys_user"."avatar" IS '头像url';
COMMENT ON COLUMN "dawfl"."sys_user"."dept_id" IS '部门id';
COMMENT ON COLUMN "dawfl"."sys_user"."birthday" IS '出生年月';
COMMENT ON COLUMN "dawfl"."sys_user"."identity_card" IS '身份证';
COMMENT ON COLUMN "dawfl"."sys_user"."sex" IS '性别 0:男 1:女';
COMMENT ON COLUMN "dawfl"."sys_user"."password" IS '密码';
COMMENT ON COLUMN "dawfl"."sys_user"."user_type" IS '类型';
COMMENT ON COLUMN "dawfl"."sys_user"."remarks" IS '备注';
COMMENT ON COLUMN "dawfl"."sys_user"."create_user_id" IS '记录创建者';
COMMENT ON COLUMN "dawfl"."sys_user"."create_date" IS '记录创建时间';
COMMENT ON COLUMN "dawfl"."sys_user"."update_user_id" IS '记录修改者';
COMMENT ON COLUMN "dawfl"."sys_user"."update_date" IS '记录修改时间';
COMMENT ON COLUMN "dawfl"."sys_user"."version" IS '记录版本';
COMMENT ON COLUMN "dawfl"."sys_user"."extend_field" IS '扩展字段';
COMMENT ON COLUMN "dawfl"."sys_user"."email" IS '邮箱地址';
COMMENT ON COLUMN "dawfl"."sys_user"."phone_number" IS '手机号码';
COMMENT ON COLUMN "dawfl"."sys_user"."user_sort" IS '显示顺序';
COMMENT ON COLUMN "dawfl"."sys_user"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "dawfl"."sys_user"."status" IS '帐号状态（0正常 1停用(锁定)）';
COMMENT ON COLUMN "dawfl"."sys_user"."del_flag" IS '删除标志（0代表存在 1代表删除）';
COMMENT ON COLUMN "dawfl"."sys_user"."login_ip" IS '最后登录IP';
COMMENT ON COLUMN "dawfl"."sys_user"."login_date" IS '最后登录时间';
COMMENT ON COLUMN "dawfl"."sys_user"."code" IS '员工编号';
COMMENT ON COLUMN "dawfl"."sys_user"."bsflag" IS '填写数据逻辑删除标识，1=在用，-5=废弃';
COMMENT ON COLUMN "dawfl"."sys_user"."create_app_id" IS '数据来源系统';
COMMENT ON COLUMN "dawfl"."sys_user"."check_date" IS '审核时间';
COMMENT ON COLUMN "dawfl"."sys_user"."check_user_id" IS '审核用户';
COMMENT ON COLUMN "dawfl"."sys_user"."data_source" IS '数据来源表CODE';
COMMENT ON COLUMN "dawfl"."sys_user"."source_data_id" IS '数据来源表主键';
COMMENT ON COLUMN "dawfl"."sys_user"."data_region" IS '油田标识';
COMMENT ON COLUMN "dawfl"."sys_user"."employee_id" IS '员工ID';
COMMENT ON TABLE "dawfl"."sys_user" IS '系统用户表';

-- ----------------------------
-- Table structure for sys_user_tenant
-- ----------------------------
DROP TABLE IF EXISTS "dawfl"."sys_user_tenant";
CREATE TABLE "dawfl"."sys_user_tenant" (
  "user_id" varchar(40) COLLATE "pg_catalog"."default",
  "tenant_id" varchar(32) COLLATE "pg_catalog"."default",
  "default_tenant" numeric(1,0)
)
;
COMMENT ON COLUMN "dawfl"."sys_user_tenant"."default_tenant" IS '默认租户,1 是,2 否';
COMMENT ON TABLE "dawfl"."sys_user_tenant" IS '用户与租户关联表';

-- ----------------------------
-- Table structure for wl_annulus_pres_relief
-- ----------------------------
DROP TABLE IF EXISTS "dawfl"."wl_annulus_pres_relief";
CREATE TABLE "dawfl"."wl_annulus_pres_relief" (
  "annulus_pres_relief_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "task_id" varchar(32) COLLATE "pg_catalog"."default",
  "well_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "annulus_name" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "relief_date" timestamp(6) NOT NULL,
  "annulus_before_pres" numeric(5,2),
  "start_time" timestamp(6) NOT NULL,
  "end_time" timestamp(6) NOT NULL,
  "annulus_after_pres" numeric(5,2),
  "annulus_pres_rcover_24hour" numeric(5,2),
  "relief_process_desc" varchar(2000) COLLATE "pg_catalog"."default",
  "bsflag" numeric(1,0) NOT NULL,
  "remarks" varchar(2000) COLLATE "pg_catalog"."default",
  "create_date" timestamp(6),
  "create_user_id" varchar(64) COLLATE "pg_catalog"."default",
  "create_app_id" varchar(64) COLLATE "pg_catalog"."default",
  "update_date" timestamp(6),
  "update_user_id" varchar(64) COLLATE "pg_catalog"."default",
  "check_date" timestamp(6),
  "check_user_id" varchar(64) COLLATE "pg_catalog"."default",
  "data_source" varchar(512) COLLATE "pg_catalog"."default",
  "source_data_id" varchar(256) COLLATE "pg_catalog"."default",
  "data_region" varchar(2) COLLATE "pg_catalog"."default",
  "tenant_id" varchar(32) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."annulus_pres_relief_id" IS '环空泄压记录ID';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."task_id" IS '任务ID，外键关联：PM_BASE_TASK';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."well_id" IS '井ID';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."annulus_name" IS '环空名称';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."relief_date" IS '泄压日期';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."annulus_before_pres" IS '放空前环空压力';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."start_time" IS '放空开始时间';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."end_time" IS '放空结束时间';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."annulus_after_pres" IS '放空后环空压力';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."annulus_pres_rcover_24hour" IS '24小时后压力恢复值';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."relief_process_desc" IS '泄压过程描述';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."bsflag" IS '填写数据逻辑删除标识，1=在用，-5=废弃';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."remarks" IS '备注';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."create_date" IS '创建日期';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."create_user_id" IS '创建用户';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."create_app_id" IS '填写数据来源的系统名';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."update_date" IS '更新日期';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."update_user_id" IS '更新用户';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."check_date" IS '记录数据在本系统的审核时间，需精确到时分秒';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."check_user_id" IS '记录数据在本系统的审核用户';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."data_source" IS '填写数据来源的表CODE';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."source_data_id" IS '存储数据来源的主键信息';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."data_region" IS '油田标识';
COMMENT ON COLUMN "dawfl"."wl_annulus_pres_relief"."tenant_id" IS '租户id';
COMMENT ON TABLE "dawfl"."wl_annulus_pres_relief" IS '环空泄压记录';

-- ----------------------------
-- Primary Key structure for table cd_attribute
-- ----------------------------
ALTER TABLE "dawfl"."cd_attribute" ADD CONSTRAINT "pk_cd_attribute" PRIMARY KEY ("attr_id");

-- ----------------------------
-- Primary Key structure for table cd_attribute_specs_value
-- ----------------------------
ALTER TABLE "dawfl"."cd_attribute_specs_value" ADD CONSTRAINT "pk_cd_attribute_specs_value" PRIMARY KEY ("value_id");

-- ----------------------------
-- Primary Key structure for table cd_organization
-- ----------------------------
ALTER TABLE "dawfl"."cd_organization" ADD CONSTRAINT "cd_organization_pkey" PRIMARY KEY ("org_id");

-- ----------------------------
-- Primary Key structure for table pm_base_task
-- ----------------------------
ALTER TABLE "dawfl"."pm_base_task" ADD CONSTRAINT "pk_task" PRIMARY KEY ("task_id");

-- ----------------------------
-- Primary Key structure for table pm_task_assignment
-- ----------------------------
ALTER TABLE "dawfl"."pm_task_assignment" ADD CONSTRAINT "pk_task_assign" PRIMARY KEY ("task_assign_id");

-- ----------------------------
-- Primary Key structure for table wl_annulus_pres_relief
-- ----------------------------
ALTER TABLE "dawfl"."wl_annulus_pres_relief" ADD CONSTRAINT "pk_annulus_pres_relief" PRIMARY KEY ("annulus_pres_relief_id");

-- ----------------------------
-- Foreign Keys structure for table cd_attribute_specs_value
-- ----------------------------
ALTER TABLE "dawfl"."cd_attribute_specs_value" ADD CONSTRAINT "fk_casv_ref_ca" FOREIGN KEY ("attr_id") REFERENCES "dawfl"."cd_attribute" ("attr_id") ON DELETE NO ACTION ON UPDATE NO ACTION;
