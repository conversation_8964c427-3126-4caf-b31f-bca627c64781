package com.cnpc.xn.cd.ugs.taskcenter.utils;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * IP地址匹配工具类，用于检查IP地址是否在给定的CIDR范围内
 *
 * <AUTHOR>
 * @date 2024/11/07
 */
public class IpMatcher {

    /**
     * IPv4地址中的八位字节数
     */
    private static final int IPV4_OCTET_COUNT = 4;

    /**
     * IPv4地址的最大CIDR前缀长度
     */
    private static final int IPV4_MAX_CIDR = 32;

    /**
     * CIDR表示法中的部分数量
     */
    private static final int CIDR_PARTS_COUNT = 2;

    /**
     * 将IPv4地址转换为长整型表示
     *
     * @param ip IPv4地址字符串
     * @return 长整型表示的IP地址
     * @throws UnknownHostException 如果IP地址格式不正确
     */
    public static long ipToLong(String ip) throws UnknownHostException {
        byte[] octets = InetAddress.getByName(ip).getAddress();
        long result = 0;
        for (int i = 0; i < IPV4_OCTET_COUNT; i++) {
            result <<= 8;
            result |= octets[i] & 0xFF;
        }
        return result;
    }

    /**
     * 计算CIDR掩码对应的数字
     *
     * @param cidr CIDR前缀长度
     * @return 长整型表示的掩码
     * @throws IllegalArgumentException 如果CIDR前缀长度不在有效范围内
     */
    public static long maskForCidr(int cidr) {
        if (cidr < 0 || cidr > IPV4_MAX_CIDR) {
            throw new IllegalArgumentException("CIDR must be between 0 and " + IPV4_MAX_CIDR);
        }
        return -1L << (IPV4_MAX_CIDR - cidr);
    }

    /**
     * 检查IP地址是否在给定的CIDR范围内
     *
     * @param ip 要检查的IP地址
     * @param cidr CIDR表示的网络范围
     * @return 如果IP地址在CIDR范围内，则返回true；否则返回false
     */
    public static boolean isIpInCidr(String ip, String cidr) {
        try {
            String[] parts = cidr.split("/");
            if (parts.length != CIDR_PARTS_COUNT) {
                //如果不是网段，则直接判断IP是否相等
                return ip.equals(cidr);
            }

            long ipLong = ipToLong(ip);
            int cidrPrefix = Integer.parseInt(parts[1]);
            long networkAddress = ipToLong(parts[0]) & maskForCidr(cidrPrefix);
            return (ipLong & maskForCidr(cidrPrefix)) == networkAddress;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        String ip = "***********";
        String cidr = "***********";
        //String cidr = "***********/24";
        if (isIpInCidr(ip, cidr)) {
            System.out.println(ip + " is in CIDR " + cidr);
        } else {
            System.out.println(ip + " is not in CIDR " + cidr);
        }
    }
}