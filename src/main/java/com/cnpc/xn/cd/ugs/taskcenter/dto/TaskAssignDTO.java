package com.cnpc.xn.cd.ugs.taskcenter.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 任务指派DTO
 */
@Data
public class TaskAssignDTO {
    
    /**
     * 任务ID
     */
    @NotBlank(message = "任务ID不能为空")
    private String taskId;
    
    /**
     * 任务分配ID
     */
    @NotBlank(message = "任务分配ID不能为空")
    private String taskAssignId;
    
    /**
     * 指派的成员ID
     */
    @NotBlank(message = "成员ID不能为空")
    private String employeeId;
    
    /**
     * 备注说明
     */
    private String remarks;
}
