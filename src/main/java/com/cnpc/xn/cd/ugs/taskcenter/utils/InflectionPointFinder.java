package com.cnpc.xn.cd.ugs.taskcenter.utils;

import java.util.ArrayList;
import java.util.List;

public class InflectionPointFinder {
    public static void main(String[] args) {
        // 示例数据点
        List<Point> points = new ArrayList<>();
        points.add(new Point(0, 0));
        points.add(new Point(1, 2));
        points.add(new Point(2, -1));
        points.add(new Point(3, 4));
        points.add(new Point(4, 2));
        points.add(new Point(5, 5));

        // 查找拐点
        List<Point> inflectionPoints = findInflectionPoints(points);

        // 输出拐点
        for (Point p : inflectionPoints) {
            System.out.println("Inflection point at: " + p);
        }
    }

    public static List<Point> findInflectionPoints(List<Point> points) {
        List<Point> inflectionPoints = new ArrayList<>();

        // 需要至少三个点才能确定一个拐点
        if (points.size() < 3) {
            return inflectionPoints;
        }

        for (int i = 1; i < points.size() - 1; i++) {
            double xPrev = points.get(i - 1).x;
            double yPrev = points.get(i - 1).y;
            double xCurr = points.get(i).x;
            double yCurr = points.get(i).y;
            double xNext = points.get(i + 1).x;
            double yNext = points.get(i + 1).y;

            // 计算一阶导数
            double dydxPrev = (yCurr - yPrev) / (xCurr - xPrev);
            double dydxNext = (yNext - yCurr) / (xNext - xCurr);

            // 计算二阶导数
            double d2ydx2 = (dydxNext - dydxPrev) / ((xNext - xPrev) / 2);

            // 检查二阶导数是否变号
            if (i > 1 && i < points.size() - 2) {
                double prevD2ydx2 = (dydxPrev - (yCurr - yPrev) / (xCurr - xPrev)) / ((xCurr - xPrev) / 2);
                if (d2ydx2 * prevD2ydx2 <= 0) {
                    inflectionPoints.add(points.get(i));
                }
            } else if (i == 1 || i == points.size() - 2) {
                // 边界情况处理
                boolean isInflectionPoint = d2ydx2 == 0;
                boolean isFirstPoint = i == 1 && d2ydx2 * (dydxNext - dydxPrev) / ((xNext - xPrev) / 2) <= 0;
                boolean isLastPoint = i == points.size() - 2 &&
                    d2ydx2 * (dydxNext - (yNext - yCurr) / (xNext - xCurr)) / ((xNext - xPrev) / 2) <= 0;

                if (isInflectionPoint || isFirstPoint || isLastPoint) {
                    inflectionPoints.add(points.get(i));
                }
            }
        }

        return inflectionPoints;
    }

    static class Point {
        double x, y;

        Point(double x, double y) {
            this.x = x;
            this.y = y;
        }

        @Override
        public String toString() {
            return "(" + x + ", " + y + ")";
        }
    }
}