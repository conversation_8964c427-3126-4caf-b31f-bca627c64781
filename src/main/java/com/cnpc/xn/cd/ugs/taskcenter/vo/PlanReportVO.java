package com.cnpc.xn.cd.ugs.taskcenter.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 计划报表视图对象
 */
@Data
public class PlanReportVO {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 组织机构ID
     */
    private String orgId;
    
    /**
     * 组织机构名称
     */
    private String orgName;
    
    /**
     * 生产阶段
     */
    private String prodPeriod;
    
    /**
     * 生产计划年月
     */
    private String prodYearMonth;
    
    /**
     * 日报表数据
     */
    private List<DailyReportVO> dailyReports;
    
    /**
     * 汇总信息
     */
    private ReportSummaryVO summary;
    
    /**
     * 提交时间
     */
    private LocalDateTime submitTime;
    
    /**
     * 提交人
     */
    private String submitter;
    
    @Data
    public static class ReportSummaryVO {
        
        /**
         * 总天数
         */
        private Integer totalDays;
        
        /**
         * 总计划量(万方)
         */
        private BigDecimal totalVolume;
        
        /**
         * 日均计划量(万方)
         */
        private BigDecimal avgDailyVolume;
    }
}
