package com.cnpc.xn.cd.ugs.taskcenter.utils;

/**
 * HTTP请求异常类
 * 
 * <AUTHOR>
 * @date 2024/11/07
 */
public class HttpRequestException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;

    /**
     * 构造函数
     * 
     * @param message 异常信息
     */
    public HttpRequestException(String message) {
        super(message);
    }

    /**
     * 构造函数
     * 
     * @param message 异常信息
     * @param cause 原始异常
     */
    public HttpRequestException(String message, Throwable cause) {
        super(message, cause);
    }
}
