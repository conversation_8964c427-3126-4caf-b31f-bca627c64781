package com.cnpc.xn.cd.ugs.taskcenter.utils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 
 *  集合助手类
 * @copyright 北京中油瑞飞信息技术有限责任公司
 * <AUTHOR>
 * @date 2016年1月10日 下午2:14:05
 *
 */
public class CollectionUtils extends org.apache.commons.collections.CollectionUtils{
	
	private CollectionUtils(){
		
	}

	
	public static boolean isEmpty(final Map<?, ?> map){
		return map == null || map.isEmpty();
	}
	
	
	public static boolean isNotEmpty(final Map<?, ?> map){
		return isEmpty(map) == false;
	}
	
	public static boolean isEmpty(final List<?> list){
		return list == null || list.isEmpty();
	}
	
	public static boolean isNotEmpty(final List<?> list){
		return isEmpty(list) == false;
	}
	
	public static boolean isEmpty(final Set<?> set){
		return set == null || set.isEmpty();
	}
	
	public static boolean isNotEmpty(final Set<?> set){
		return isEmpty(set) == false;
	}
	
	/**
	 * 返回集合中的第一个元素
	 * @param collection
	 * @return
	 */
	public static Object uniqueResult(final Collection<?> collection){
		if(false == CollectionUtils.isEmpty(collection)){
			return collection.iterator().next();
		}
		return null;
	}
}
