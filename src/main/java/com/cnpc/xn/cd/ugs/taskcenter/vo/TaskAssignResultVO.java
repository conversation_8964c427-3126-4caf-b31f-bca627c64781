package com.cnpc.xn.cd.ugs.taskcenter.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 任务指派结果视图对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaskAssignResultVO {
    
    /**
     * 任务分配ID
     */
    private String taskAssignId;
    
    /**
     * 指派时间
     */
    private LocalDateTime assignTime;
}
