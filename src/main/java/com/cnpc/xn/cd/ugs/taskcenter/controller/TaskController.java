package com.cnpc.xn.cd.ugs.taskcenter.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cnpc.xn.cd.ugs.taskcenter.dto.*;
import com.cnpc.xn.cd.ugs.taskcenter.service.TaskService;
import com.cnpc.xn.cd.ugs.taskcenter.vo.*;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 任务管理控制器
 */
@RestController
@RequestMapping("/api/pc/v1/tasks")
@RequiredArgsConstructor
public class TaskController {

    private final TaskService taskService;

    /**
     * 创建任务
     */
    @PostMapping("/create")
    public ResponseEntity<ApiResult<TaskCreateResultVO>> createTask(@RequestBody @Valid TaskCreateDTO createDTO) {
        TaskCreateResultVO result = taskService.createTask(createDTO);
        return ResponseEntity.ok(ApiResult.success("创建成功", result));
    }

    /**
     * 修改任务
     */
    @PostMapping("/update")
    public ResponseEntity<ApiResult<TaskUpdateResultVO>> updateTask(
            @RequestParam("taskId") String taskId,
            @RequestBody @Valid TaskUpdateDTO updateDTO) {
        TaskUpdateResultVO result = taskService.updateTask(taskId, updateDTO);
        return ResponseEntity.ok(ApiResult.success("修改成功", result));
    }

    /**
     * 删除任务
     */
    @PostMapping("/delete")
    public ResponseEntity<ApiResult<String>> deleteTask(@RequestParam("taskId") String taskId) {
        taskService.deleteTask(taskId);
        return ResponseEntity.ok(ApiResult.success("删除成功"));
    }

    /**
     * 指派任务
     */
    @PostMapping("/assignments/assign")
    public ResponseEntity<ApiResult<TaskAssignResultVO>> assignTask(@RequestBody @Valid TaskAssignDTO assignDTO) {
        TaskAssignResultVO result = taskService.assignTask(assignDTO);
        return ResponseEntity.ok(ApiResult.success("指派成功", result));
    }

    /**
     * 调整任务指派
     */
    @PostMapping("/assignments/reassign")
    public ResponseEntity<ApiResult<TaskReassignResultVO>> reassignTask(
            @RequestParam("taskAssignId") String taskAssignId,
            @RequestBody @Valid TaskReassignDTO reassignDTO) {
        TaskReassignResultVO result = taskService.reassignTask(taskAssignId, reassignDTO);
        return ResponseEntity.ok(ApiResult.success("调整成功", result));
    }

    /**
     * 保存草稿
     */
    @PostMapping("/reports/draft/save")
    public ResponseEntity<ApiResult<PlanDraftResultVO>> saveDraft(@RequestBody @Valid PlanDraftDTO draftDTO) {
        PlanDraftResultVO result = taskService.saveDraft(draftDTO);
        return ResponseEntity.ok(ApiResult.success("保存成功", result));
    }

    /**
     * 提交填报
     */
    @PostMapping("/reports/submit")
    public ResponseEntity<ApiResult<PlanSubmitResultVO>> submitPlan(@RequestBody @Valid PlanDraftDTO draftDTO) {
        PlanSubmitResultVO result = taskService.submitPlan(draftDTO);
        return ResponseEntity.ok(ApiResult.success("提交成功", result));
    }

    /**
     * 获取草稿
     */
    @GetMapping("/reports/draft")
    public ResponseEntity<ApiResult<PlanDraftVO>> getDraft(
            @RequestParam("taskId") String taskId,
            @RequestParam("taskAssignId") String taskAssignId,
            @RequestParam("employeeId") String employeeId) {
        PlanDraftVO result = taskService.getDraft(taskId, taskAssignId, employeeId);
        return ResponseEntity.ok(ApiResult.success(result));
    }

    /**
     * 分页查询任务列表
     */
    @PostMapping("/page")
    public ResponseEntity<ApiResult<IPage<TaskVO>>> pageTask(@RequestBody TaskQueryDTO queryDTO) {
        IPage<TaskVO> result = taskService.pageTask(queryDTO);
        return ResponseEntity.ok(ApiResult.success(result));
    }

    /**
     * 获取任务详情
     */
    @GetMapping("/detail")
    public ResponseEntity<ApiResult<TaskDetailVO>> getTaskDetail(@RequestParam("taskId") String taskId) {
        TaskDetailVO result = taskService.getTaskDetail(taskId);
        return ResponseEntity.ok(ApiResult.success(result));
    }

    /**
     * 获取任务日志
     */
    @GetMapping("/logs")
    public ResponseEntity<ApiResult<List<TaskLogVO>>> getTaskLogs(@RequestParam("taskId") String taskId) {
        List<TaskLogVO> result = taskService.getTaskLogs(taskId);
        return ResponseEntity.ok(ApiResult.success(result));
    }

    /**
     * 获取填报结果
     */
    @GetMapping("/reports")
    public ResponseEntity<ApiResult<PlanReportVO>> getPlanReport(
            @RequestParam("taskId") String taskId,
            @RequestParam("orgId") String orgId) {
        PlanReportVO result = taskService.getPlanReport(taskId, orgId);
        return ResponseEntity.ok(ApiResult.success(result));
    }
}
