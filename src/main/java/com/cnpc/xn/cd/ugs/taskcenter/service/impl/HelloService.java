package com.cnpc.xn.cd.ugs.taskcenter.service.impl;

import cnpc.udp.framework.mybatis.core.annotation.UdpDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName HelloService
 * <p>
 * 测试服务
 * </p>
 * @date 2024/10/28 15:39
 **/
@Service
@UdpDataSource(value = "pg")
public class HelloService {
    @Autowired
    private JdbcTemplate jdbcTemplate;
    public List helloPg() {
        List list = jdbcTemplate.queryForList("SELECT pt.project_id FROM dz_def_crp.X_CD_WELL well LEFT JOIN dz_def_crp.x_cd_geo_unit pt ON ( pt.project_id = well.PLATFORM_ID AND pt.unit_level_id = 'ATSVZB100012246' )", String.class);
        return list;
    }

    /**
     * 从Oracle数据库中查询数据的方法
     * @return 包含查询结果的List对象，其中每个元素都是一个字符串
     */
    @UdpDataSource(value = "oracle")
    public List helloOracle() {
        List list = jdbcTemplate.queryForList("SELECT jinghao FROM c##kfscsjzl12.pc_well_v where rownum <=10", String.class);
        return list;
    }
}
