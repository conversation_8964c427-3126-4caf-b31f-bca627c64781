package com.cnpc.xn.cd.ugs.taskcenter.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务视图对象
 */
@Data
public class TaskVO {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务小类
     */
    private String subTaskClass;

    /**
     * 计划年月
     */
    private String planYearMonth;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 组织机构ID
     */
    private String orgId;

    /**
     * 组织机构名称
     */
    private String orgName;

    /**
     * 任务状态
     */
    private String taskStatus;

    /**
     * 任务状态名称
     */
    private String taskStatusName;

    /**
     * 任务分配信息列表
     */
    private List<TaskAssignmentVO> assignments;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
