package com.cnpc.xn.cd.ugs.taskcenter.utils;


import java.util.ArrayList;
import java.util.List;

class Point {
    double x, y;

    Point(double x, double y) {
        this.x = x;
        this.y = y;
    }
}
/**
 * <AUTHOR>
 * @ClassName MathFunctionUtil
 * <p>
 * 数据公式工具
 * </p>
 * @date 2024/11/06 18:30
 **/
public class InflectionPointFinderUtil {

    // 计算并返回给定点的二阶导数
    private static double secondDerivative(List<Point> points, int i) {
        if (i < 1 || i >= points.size() - 1) {
            throw new IndexOutOfBoundsException("Index out of bounds for computing second derivative.");
        }
        double dx = points.get(i + 1).x - points.get(i - 1).x;
        double dy1 = points.get(i + 1).y - points.get(i).y;
        double dy2 = points.get(i).y - points.get(i - 1).y;
        return (dy1 - dy2) / (dx * dx);
    }

    // 计算并返回给定点的一阶导数
    private static double firstDerivative(List<Point> points, int i) {
        if (i < 0 || i >= points.size() - 1) {
            throw new IndexOutOfBoundsException("Index out of bounds for computing first derivative.");
        }
        double dx = points.get(i + 1).x - points.get(i).x;
        double dy = points.get(i + 1).y - points.get(i).y;
        return dy / dx;
    }

    // 查找拐点
    public static List<Point> findInflectionPoints(List<Point> points) {
        List<Point> inflectionPoints = new ArrayList<>();

        for (int i = 1; i < points.size() - 1; i++) {
            double secondDeriv = secondDerivative(points, i);
            if (Math.abs(secondDeriv) < 1e-6) { // 判断是否接近0，避免浮点数精度问题
                double firstDerivPrev = firstDerivative(points, i - 1);
                double firstDerivNext = firstDerivative(points, i + 1);

                if ((firstDerivPrev > 0 && firstDerivNext < 0) || (firstDerivPrev < 0 && firstDerivNext > 0)) {
                    inflectionPoints.add(points.get(i));
                }
            }
        }

        return inflectionPoints;
    }

    public static void main(String[] args) {
        List<Point> points = new ArrayList<>();
        // 示例数据点，需要根据实际情况填充
        points.add(new Point(1, 2));
        points.add(new Point(2, 3));
        points.add(new Point(3, 2));
        points.add(new Point(4, 3));
        points.add(new Point(5, 6));
        points.add(new Point(6, 5));
        points.add(new Point(7, 8));

        List<Point> inflectionPoints = findInflectionPoints(points);

        System.out.println("Inflection Points:");
        for (Point p : inflectionPoints) {
            System.out.println("(" + p.x + ", " + p.y + ")");
        }
    }
}