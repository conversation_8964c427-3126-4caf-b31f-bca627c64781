package com.cnpc.xn.cd.ugs.taskcenter.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务分配实体类
 */
@Data
@TableName("dawfl.pm_task_assignment")
public class TaskAssignment {

    /**
     * 任务分配ID
     */
    @TableId(value = "task_assign_id", type = IdType.INPUT)
    private String taskAssignId;

    /**
     * 任务ID，外键关联：PM_BASE_TASK
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 组织机构ID，外键关联：CD_ORGANIZATION
     */
    @TableField("org_id")
    private String orgId;

    /**
     * 负责人ID，外键关联：SYS_USER
     */
    @TableField("manager_id")
    private String managerId;

    /**
     * 人员ID，外键关联：SYS_USER
     */
    @TableField("employee_id")
    private String employeeId;

    /**
     * 任务状态，1已完成；2进行中；3已超期
     */
    @TableField("task_status")
    private String taskStatus;

    /**
     * 任务完成时间
     */
    @TableField("finish_time")
    private LocalDateTime finishTime;

    /**
     * 数据逻辑删除标识，1=在用，-5=废弃
     */
    @TableField("bsflag")
    private Integer bsflag;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 创建日期
     */
    @TableField("create_date")
    private LocalDateTime createDate;

    /**
     * 创建用户
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     * 填写数据来源的系统名
     */
    @TableField("create_app_id")
    private String createAppId;

    /**
     * 更新日期
     */
    @TableField("update_date")
    private LocalDateTime updateDate;

    /**
     * 更新用户
     */
    @TableField("update_user_id")
    private String updateUserId;

    /**
     * 记录数据在本系统的审核时间，需精确到时分秒
     */
    @TableField("check_date")
    private LocalDateTime checkDate;

    /**
     * 记录数据在本系统的审核用户
     */
    @TableField("check_user_id")
    private String checkUserId;

    /**
     * 填写数据来源的表CODE
     */
    @TableField("data_source")
    private String dataSource;

    /**
     * 存储数据来源的主键信息
     */
    @TableField("source_data_id")
    private String sourceDataId;

    /**
     * 油田标识
     */
    @TableField("data_region")
    private String dataRegion;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;
}
