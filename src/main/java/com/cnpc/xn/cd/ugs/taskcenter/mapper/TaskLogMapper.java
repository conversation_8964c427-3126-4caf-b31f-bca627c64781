package com.cnpc.xn.cd.ugs.taskcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cnpc.xn.cd.ugs.taskcenter.entity.TaskLog;
import com.cnpc.xn.cd.ugs.taskcenter.vo.TaskLogVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 任务日志Mapper接口
 */
@Mapper
public interface TaskLogMapper extends BaseMapper<TaskLog> {
    
    /**
     * 根据任务ID查询任务日志
     *
     * @param taskId 任务ID
     * @return 任务日志列表
     */
    List<TaskLogVO> listByTaskId(@Param("taskId") String taskId);
}
