package com.cnpc.xn.cd.ugs.taskcenter.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * API响应结果
 *
 * @param <T> 数据类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiResult<T> {
    
    /**
     * 响应码，0表示成功，非0表示失败
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 成功响应
     *
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> ApiResult<T> success() {
        return new ApiResult<>(0, "", null);
    }
    
    /**
     * 成功响应
     *
     * @param data 响应数据
     * @param <T>  数据类型
     * @return 响应结果
     */
    public static <T> ApiResult<T> success(T data) {
        return new ApiResult<>(0, "", data);
    }
    
    /**
     * 成功响应
     *
     * @param msg  响应消息
     * @param data 响应数据
     * @param <T>  数据类型
     * @return 响应结果
     */
    public static <T> ApiResult<T> success(String msg, T data) {
        return new ApiResult<>(0, msg, data);
    }
    
    /**
     * 失败响应
     *
     * @param code 响应码
     * @param msg  响应消息
     * @param <T>  数据类型
     * @return 响应结果
     */
    public static <T> ApiResult<T> error(Integer code, String msg) {
        return new ApiResult<>(code, msg, null);
    }
}
