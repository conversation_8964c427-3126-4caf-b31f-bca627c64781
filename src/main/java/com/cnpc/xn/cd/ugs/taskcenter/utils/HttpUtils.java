package com.cnpc.xn.cd.ugs.taskcenter.utils;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;


import java.util.Map;

/**
 *
 * <AUTHOR>
 *
 */
public class HttpUtils {

	private HttpUtils(){}

	public static String addUrlParameter(final String url, final String name, final Object value){
		final StringBuffer sb = new StringBuffer();
		final int index = url.indexOf("?");
		if(index == -1){
			sb.append(url).append("?").append(name).append("=").append(value == null ? "" : value);
		}else{
			final String prefix = url.substring(0, index);
			sb.append(prefix).append("?").append(name).append("=").append(value == null ? "" : value);
			sb.append("&");
			sb.append(url.substring(index + 1));
		}
		return sb.toString();
	}

	public static String addUrlParameters(String url, final Map<String, Object> params, final String[] excludeParamNames){
		for(final Map.Entry<String, Object> entry : params.entrySet()){
			final String name = entry.getKey();
			Object value = entry.getValue();
			if(excludeParamNames != null && ArrayUtils.contains(excludeParamNames, name)){
				continue;
			}
			if(value != null){
				value = java.net.URLEncoder.encode(String.valueOf(value));
			}
			url = HttpUtils.addUrlParameter(url, name, value);
		}
		return url;
	}

	public static void deleteCookie(final String name, final HttpServletResponse response){
		final Cookie cookie = new Cookie(name, null);
		cookie.setMaxAge(0);
		response.addCookie(cookie);
	}

	public static Cookie getCookie(final String name, final HttpServletRequest request){
		final Cookie[] cookies = request.getCookies();
		for(final Cookie cookie : cookies){
			if(StringUtils.equals(name, cookie.getName())){
				return cookie;
			}
		}
		return null;
	}
}
