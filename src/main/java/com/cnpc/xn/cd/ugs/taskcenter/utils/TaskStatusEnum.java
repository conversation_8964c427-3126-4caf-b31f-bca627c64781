package com.cnpc.xn.cd.ugs.taskcenter.utils;

import lombok.Getter;

/**
 * 任务状态枚举
 */
@Getter
public enum TaskStatusEnum {
    
    /**
     * 已完成
     */
    COMPLETED("1", "已完成"),
    
    /**
     * 进行中
     */
    IN_PROGRESS("2", "进行中"),
    
    /**
     * 已超期
     */
    OVERDUE("3", "已超期");
    
    /**
     * 状态码
     */
    private final String code;
    
    /**
     * 状态名称
     */
    private final String name;
    
    TaskStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    /**
     * 根据状态码获取状态名称
     *
     * @param code 状态码
     * @return 状态名称
     */
    public static String getNameByCode(String code) {
        for (TaskStatusEnum status : TaskStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getName();
            }
        }
        return "";
    }
}
