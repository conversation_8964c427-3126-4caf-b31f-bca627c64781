package com.cnpc.xn.cd.ugs.taskcenter.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务日志实体类
 */
@Data
@TableName("dawfl.pm_task_log")
public class TaskLog {
    
    /**
     * 日志ID
     */
    @TableId(value = "log_id", type = IdType.INPUT)
    private String logId;
    
    /**
     * 任务ID
     */
    @TableField("task_id")
    private String taskId;
    
    /**
     * 任务分配ID
     */
    @TableField("task_assign_id")
    private String taskAssignId;
    
    /**
     * 操作类型
     */
    @TableField("operation_type")
    private String operationType;
    
    /**
     * 操作时间
     */
    @TableField("operation_time")
    private LocalDateTime operationTime;
    
    /**
     * 操作人 ID
     */
    @TableField("operator_id")
    private String operatorId;
    
    /**
     * 操作描述
     */
    @TableField("operation_desc")
    private String operationDesc;
    
    /**
     * 数据逻辑删除标识，1=在用，-5=废弃
     */
    @TableField("bsflag")
    private Integer bsflag;
    
    /**
     * 创建时间
     */
    @TableField("create_date")
    private LocalDateTime createDate;
    
    /**
     * 创建用户
     */
    @TableField("create_user_id")
    private String createUserId;
    
    /**
     * 更新时间
     */
    @TableField("update_date")
    private LocalDateTime updateDate;
    
    /**
     * 更新用户
     */
    @TableField("update_user_id")
    private String updateUserId;
    
    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;
}
