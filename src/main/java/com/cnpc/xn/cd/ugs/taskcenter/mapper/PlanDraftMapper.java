package com.cnpc.xn.cd.ugs.taskcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cnpc.xn.cd.ugs.taskcenter.entity.PlanDraft;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 储气库生产运行计划草稿Mapper接口
 */
@Mapper
public interface PlanDraftMapper extends BaseMapper<PlanDraft> {
    
    /**
     * 根据任务ID和填报人员ID查询草稿
     *
     * @param taskId 任务ID
     * @param taskAssignId 任务分配ID
     * @param employeeId 填报人员ID
     * @return 草稿信息
     */
    PlanDraft getByTaskAndEmployee(@Param("taskId") String taskId, 
                                  @Param("taskAssignId") String taskAssignId, 
                                  @Param("employeeId") String employeeId);
}
