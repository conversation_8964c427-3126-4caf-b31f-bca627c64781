package com.cnpc.xn.cd.ugs.taskcenter.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务查询DTO
 */
@Data
public class TaskQueryDTO {
    
    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 每页记录数
     */
    private Integer pageSize = 10;
    
    /**
     * 任务类型：admin(管理员)、manager(负责人)、employee(员工)
     */
    private String roleType;
    
    /**
     * 组织机构ID
     */
    private String orgId;
    
    /**
     * 任务小类
     */
    private String subTaskClass;
    
    /**
     * 计划年月
     */
    private String planYearMonth;
    
    /**
     * 任务状态，1(已完成)、2(进行中)、3(已超期)
     */
    private String taskStatus;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
}
