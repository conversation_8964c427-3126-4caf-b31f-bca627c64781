package com.cnpc.xn.cd.ugs.taskcenter.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务基本信息实体类
 */
@Data
@TableName("dawfl.pm_base_task")
public class BaseTask {
    
    /**
     * 任务ID
     */
    @TableId(value = "task_id", type = IdType.INPUT)
    private String taskId;
    
    /**
     * 父任务ID
     */
    @TableField("parent_task_id")
    private String parentTaskId;
    
    /**
     * 组织机构ID
     */
    @TableField("org_id")
    private String orgId;
    
    /**
     * 任务大类
     */
    @TableField("task_class")
    private String taskClass;
    
    /**
     * 任务小类
     */
    @TableField("sub_task_class")
    private String subTaskClass;
    
    /**
     * 任务名称
     */
    @TableField("task_name")
    private String taskName;
    
    /**
     * 任务描述
     */
    @TableField("task_desc")
    private String taskDesc;
    
    /**
     * 要求开始日期
     */
    @TableField("start_time")
    private LocalDateTime startTime;
    
    /**
     * 要求结束日期
     */
    @TableField("end_time")
    private LocalDateTime endTime;
    
    /**
     * 发布人
     */
    @TableField("sender")
    private String sender;
    
    /**
     * 发布时间
     */
    @TableField("send_date")
    private LocalDateTime sendDate;
    
    /**
     * 是否多人协作，1是，0否
     */
    @TableField("is_supervised")
    private String isSupervised;
    
    /**
     * 附件（多个）
     */
    @TableField("attachment_file_id")
    private String attachmentFileId;
    
    /**
     * 任务状态，1已完成；2进行中；3已超期
     */
    @TableField("task_status")
    private String taskStatus;
    
    /**
     * 任务完成时间
     */
    @TableField("finish_time")
    private LocalDateTime finishTime;
    
    /**
     * 数据逻辑删除标识，1=在用，-5=废弃
     */
    @TableField("bsflag")
    private Integer bsflag;
    
    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;
    
    /**
     * 创建日期
     */
    @TableField("create_date")
    private LocalDateTime createDate;
    
    /**
     * 创建用户
     */
    @TableField("create_user_id")
    private String createUserId;
    
    /**
     * 填写数据来源的系统名
     */
    @TableField("create_app_id")
    private String createAppId;
    
    /**
     * 更新日期
     */
    @TableField("update_date")
    private LocalDateTime updateDate;
    
    /**
     * 更新用户
     */
    @TableField("update_user_id")
    private String updateUserId;
    
    /**
     * 记录数据在本系统的审核时间，需精确到时分秒
     */
    @TableField("check_date")
    private LocalDateTime checkDate;
    
    /**
     * 记录数据在本系统的审核用户
     */
    @TableField("check_user_id")
    private String checkUserId;
    
    /**
     * 填写数据来源的表CODE
     */
    @TableField("data_source")
    private String dataSource;
    
    /**
     * 存储数据来源的主键信息
     */
    @TableField("source_data_id")
    private String sourceDataId;
    
    /**
     * 油田标识
     */
    @TableField("data_region")
    private String dataRegion;
    
    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;
}
