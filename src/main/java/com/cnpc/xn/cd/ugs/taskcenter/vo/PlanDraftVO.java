package com.cnpc.xn.cd.ugs.taskcenter.vo;

import com.cnpc.xn.cd.ugs.taskcenter.dto.PeriodReportDTO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 计划草稿视图对象
 */
@Data
public class PlanDraftVO {
    
    /**
     * 草稿ID
     */
    private String draftId;
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务分配ID
     */
    private String taskAssignId;
    
    /**
     * 组织机构ID
     */
    private String orgId;
    
    /**
     * 填报人员ID
     */
    private String employeeId;
    
    /**
     * 生产阶段
     */
    private String prodPeriod;
    
    /**
     * 生产计划年月
     */
    private String prodYearMonth;
    
    /**
     * 时段填报数据
     */
    private List<PeriodReportDTO> periodReports;
    
    /**
     * 备注
     */
    private String remarks;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
