package com.cnpc.xn.cd.ugs.taskcenter.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务详情视图对象
 */
@Data
public class TaskDetailVO {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务大类
     */
    private String taskClass;
    
    /**
     * 任务小类
     */
    private String subTaskClass;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 计划年月
     */
    private String planYearMonth;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 任务说明
     */
    private String taskDesc;
    
    /**
     * 任务状态
     */
    private String taskStatus;
    
    /**
     * 任务状态名称
     */
    private String taskStatusName;
    
    /**
     * 发布人
     */
    private String sender;
    
    /**
     * 发布时间
     */
    private LocalDateTime sendDate;
    
    /**
     * 是否多人协作
     */
    private String isSupervised;
    
    /**
     * 附件ID
     */
    private String attachmentFileId;
    
    /**
     * 完成时间
     */
    private LocalDateTime finishTime;
    
    /**
     * 任务分配信息
     */
    private List<TaskAssignmentVO> assignments;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建用户
     */
    private String createUser;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新用户
     */
    private String updateUser;
}
