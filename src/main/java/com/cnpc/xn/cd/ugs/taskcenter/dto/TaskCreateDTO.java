package com.cnpc.xn.cd.ugs.taskcenter.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务创建DTO
 */
@Data
public class TaskCreateDTO {

    /**
     * 任务大类，固定为"计划上报"
     */
    @NotBlank(message = "任务大类不能为空")
    private String taskClass;

    /**
     * 任务小类，"月度采气计划"或"月度注气计划"
     */
    @NotBlank(message = "任务小类不能为空")
    private String subTaskClass;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String taskName;

    /**
     * 计划年月，格式：YYYY-MM
     */
    @NotBlank(message = "计划年月不能为空")
    private String planYearMonth;

    /**
     * 开始日期
     */
    @NotNull(message = "开始日期不能为空")
    private LocalDateTime startTime;

    /**
     * 结束日期
     */
    @NotNull(message = "结束日期不能为空")
    private LocalDateTime endTime;

    /**
     * 任务说明
     */
    private String taskDesc;

    /**
     * 组织机构ID
     */
    @NotBlank(message = "组织机构ID不能为空")
    private String orgId;

    /**
     * 任务分配信息
     */
    @NotEmpty(message = "任务分配信息不能为空")
    @Valid
    private List<TaskAssignmentDTO> assignments;
}
