package com.cnpc.xn.cd.ugs.taskcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cnpc.xn.cd.ugs.taskcenter.dto.*;
import com.cnpc.xn.cd.ugs.taskcenter.entity.BaseTask;
import com.cnpc.xn.cd.ugs.taskcenter.entity.PlanDraft;
import com.cnpc.xn.cd.ugs.taskcenter.entity.TaskAssignment;
import com.cnpc.xn.cd.ugs.taskcenter.entity.TaskLog;
import com.cnpc.xn.cd.ugs.taskcenter.mapper.BaseTaskMapper;
import com.cnpc.xn.cd.ugs.taskcenter.mapper.PlanDraftMapper;
import com.cnpc.xn.cd.ugs.taskcenter.mapper.TaskAssignmentMapper;
import com.cnpc.xn.cd.ugs.taskcenter.mapper.TaskLogMapper;
import com.cnpc.xn.cd.ugs.taskcenter.service.TaskService;
import com.cnpc.xn.cd.ugs.taskcenter.utils.SecurityUtils;
import com.cnpc.xn.cd.ugs.taskcenter.utils.TaskStatusEnum;
import com.cnpc.xn.cd.ugs.taskcenter.vo.*;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 任务管理服务实现类
 */
@Service
@RequiredArgsConstructor
public class TaskServiceImpl implements TaskService {

    private final BaseTaskMapper baseTaskMapper;
    private final TaskAssignmentMapper taskAssignmentMapper;
    private final TaskLogMapper taskLogMapper;
    private final PlanDraftMapper planDraftMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskCreateResultVO createTask(TaskCreateDTO createDTO) {
        // 1. 创建任务基本信息
        BaseTask baseTask = new BaseTask();
        BeanUtils.copyProperties(createDTO, baseTask);

        // 生成任务ID
        String taskId = generateTaskId();
        baseTask.setTaskId(taskId);

        // 将计划年月设置到remarks字段中
        baseTask.setRemarks(createDTO.getPlanYearMonth());

        // 设置任务状态为进行中
        baseTask.setTaskStatus(TaskStatusEnum.IN_PROGRESS.getCode());

        // 设置发布人和发布时间
        String currentUserId = SecurityUtils.getCurrentUserId();
        baseTask.setSender(currentUserId);
        baseTask.setSendDate(LocalDateTime.now());

        // 设置创建和更新信息
        LocalDateTime now = LocalDateTime.now();
        baseTask.setCreateDate(now);
        baseTask.setCreateUserId(currentUserId);
        baseTask.setUpdateDate(now);
        baseTask.setUpdateUserId(currentUserId);
        baseTask.setBsflag(1);

        // 保存任务基本信息
        baseTaskMapper.insert(baseTask);

        // 2. 创建任务分配信息
        for (TaskAssignmentDTO assignmentDTO : createDTO.getAssignments()) {
            TaskAssignment assignment = new TaskAssignment();
            assignment.setTaskId(taskId);
            assignment.setTaskAssignId(generateTaskAssignId());
            assignment.setOrgId(assignmentDTO.getOrgId());
            assignment.setManagerId(assignmentDTO.getManagerId()); // 设置负责人
            assignment.setEmployeeId(assignmentDTO.getManagerId()); // 初始分配给负责人
            assignment.setTaskStatus(TaskStatusEnum.IN_PROGRESS.getCode());
            assignment.setRemarks(assignmentDTO.getRemarks());
            assignment.setCreateDate(now);
            assignment.setCreateUserId(currentUserId);
            assignment.setUpdateDate(now);
            assignment.setUpdateUserId(currentUserId);
            assignment.setBsflag(1);

            taskAssignmentMapper.insert(assignment);
        }

        // 3. 记录任务创建日志
        TaskLog taskLog = new TaskLog();
        taskLog.setLogId(generateLogId());
        taskLog.setTaskId(taskId);
        taskLog.setOperationType("创建");
        taskLog.setOperationTime(now);
        taskLog.setOperatorId(currentUserId);
        taskLog.setOperationDesc("创建任务并分配");
        taskLog.setBsflag(1);
        taskLog.setCreateDate(now);
        taskLog.setCreateUserId(currentUserId);
        taskLog.setUpdateDate(now);
        taskLog.setUpdateUserId(currentUserId);

        taskLogMapper.insert(taskLog);

        return new TaskCreateResultVO(taskId, now);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskUpdateResultVO updateTask(String taskId, TaskUpdateDTO updateDTO) {
        // 1. 查询任务是否存在
        BaseTask baseTask = baseTaskMapper.selectById(taskId);
        if (baseTask == null) {
            throw new RuntimeException("任务不存在");
        }

        // 2. 更新任务基本信息
        BeanUtils.copyProperties(updateDTO, baseTask);

        // 将计划年月设置到remarks字段中
        if (updateDTO.getPlanYearMonth() != null) {
            baseTask.setRemarks(updateDTO.getPlanYearMonth());
        }

        // 设置更新信息
        String currentUserId = SecurityUtils.getCurrentUserId();
        LocalDateTime now = LocalDateTime.now();
        baseTask.setUpdateDate(now);
        baseTask.setUpdateUserId(currentUserId);

        baseTaskMapper.updateById(baseTask);

        // 3. 记录任务更新日志
        TaskLog taskLog = new TaskLog();
        taskLog.setLogId(generateLogId());
        taskLog.setTaskId(taskId);
        taskLog.setOperationType("修改");
        taskLog.setOperationTime(now);
        taskLog.setOperatorId(currentUserId);
        taskLog.setOperationDesc("修改任务信息");
        taskLog.setBsflag(1);
        taskLog.setCreateDate(now);
        taskLog.setCreateUserId(currentUserId);
        taskLog.setUpdateDate(now);
        taskLog.setUpdateUserId(currentUserId);

        taskLogMapper.insert(taskLog);

        return new TaskUpdateResultVO(taskId, now);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTask(String taskId) {
        // 1. 查询任务是否存在
        BaseTask baseTask = baseTaskMapper.selectById(taskId);
        if (baseTask == null) {
            throw new RuntimeException("任务不存在");
        }

        // 2. 逻辑删除任务基本信息
        baseTask.setBsflag(-5);
        baseTaskMapper.updateById(baseTask);

        // 3. 逻辑删除任务分配信息
        LambdaQueryWrapper<TaskAssignment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskAssignment::getTaskId, taskId);
        List<TaskAssignment> assignments = taskAssignmentMapper.selectList(wrapper);

        for (TaskAssignment assignment : assignments) {
            assignment.setBsflag(-5);
            taskAssignmentMapper.updateById(assignment);
        }

        // 4. 记录任务删除日志
        String currentUserId = SecurityUtils.getCurrentUserId();
        LocalDateTime now = LocalDateTime.now();

        TaskLog taskLog = new TaskLog();
        taskLog.setLogId(generateLogId());
        taskLog.setTaskId(taskId);
        taskLog.setOperationType("删除");
        taskLog.setOperationTime(now);
        taskLog.setOperatorId(currentUserId);
        taskLog.setOperationDesc("删除任务");
        taskLog.setBsflag(1);
        taskLog.setCreateDate(now);
        taskLog.setCreateUserId(currentUserId);
        taskLog.setUpdateDate(now);
        taskLog.setUpdateUserId(currentUserId);

        taskLogMapper.insert(taskLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskAssignResultVO assignTask(TaskAssignDTO assignDTO) {
        // 1. 查询任务分配信息是否存在
        TaskAssignment assignment = taskAssignmentMapper.selectById(assignDTO.getTaskAssignId());
        if (assignment == null) {
            throw new RuntimeException("任务分配信息不存在");
        }

        // 2. 更新任务分配信息
        assignment.setEmployeeId(assignDTO.getEmployeeId());
        assignment.setRemarks(assignDTO.getRemarks());

        // 设置更新信息
        String currentUserId = SecurityUtils.getCurrentUserId();
        LocalDateTime now = LocalDateTime.now();
        assignment.setUpdateDate(now);
        assignment.setUpdateUserId(currentUserId);

        taskAssignmentMapper.updateById(assignment);

        // 3. 记录任务指派日志
        TaskLog taskLog = new TaskLog();
        taskLog.setLogId(generateLogId());
        taskLog.setTaskId(assignDTO.getTaskId());
        taskLog.setTaskAssignId(assignDTO.getTaskAssignId());
        taskLog.setOperationType("指派");
        taskLog.setOperationTime(now);
        taskLog.setOperatorId(currentUserId);
        taskLog.setOperationDesc("指派任务给成员");
        taskLog.setBsflag(1);
        taskLog.setCreateDate(now);
        taskLog.setCreateUserId(currentUserId);
        taskLog.setUpdateDate(now);
        taskLog.setUpdateUserId(currentUserId);

        taskLogMapper.insert(taskLog);

        return new TaskAssignResultVO(assignDTO.getTaskAssignId(), now);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskReassignResultVO reassignTask(String taskAssignId, TaskReassignDTO reassignDTO) {
        // 1. 查询任务分配信息是否存在
        TaskAssignment assignment = taskAssignmentMapper.selectById(taskAssignId);
        if (assignment == null) {
            throw new RuntimeException("任务分配信息不存在");
        }

        // 2. 更新任务分配信息
        assignment.setEmployeeId(reassignDTO.getEmployeeId());
        assignment.setRemarks(reassignDTO.getRemarks());

        // 设置更新信息
        String currentUserId = SecurityUtils.getCurrentUserId();
        LocalDateTime now = LocalDateTime.now();
        assignment.setUpdateDate(now);
        assignment.setUpdateUserId(currentUserId);

        taskAssignmentMapper.updateById(assignment);

        // 3. 记录任务重新指派日志
        TaskLog taskLog = new TaskLog();
        taskLog.setLogId(generateLogId());
        taskLog.setTaskId(assignment.getTaskId());
        taskLog.setTaskAssignId(taskAssignId);
        taskLog.setOperationType("重新指派");
        taskLog.setOperationTime(now);
        taskLog.setOperatorId(currentUserId);
        taskLog.setOperationDesc("调整任务指派");
        taskLog.setBsflag(1);
        taskLog.setCreateDate(now);
        taskLog.setCreateUserId(currentUserId);
        taskLog.setUpdateDate(now);
        taskLog.setUpdateUserId(currentUserId);

        taskLogMapper.insert(taskLog);

        return new TaskReassignResultVO(taskAssignId, now);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PlanDraftResultVO saveDraft(PlanDraftDTO draftDTO) {
        // 1. 查询是否已存在草稿
        PlanDraft existingDraft = planDraftMapper.getByTaskAndEmployee(
                draftDTO.getTaskId(), draftDTO.getTaskAssignId(), draftDTO.getEmployeeId());

        String currentUserId = SecurityUtils.getCurrentUserId();
        LocalDateTime now = LocalDateTime.now();

        // 2. 保存或更新草稿
        if (existingDraft == null) {
            // 创建新草稿
            PlanDraft planDraft = new PlanDraft();
            planDraft.setDraftId(generateDraftId());
            planDraft.setTaskId(draftDTO.getTaskId());
            planDraft.setTaskAssignId(draftDTO.getTaskAssignId());
            planDraft.setOrgId(draftDTO.getOrgId());
            planDraft.setEmployeeId(draftDTO.getEmployeeId());
            planDraft.setProdPeriod(draftDTO.getProdPeriod());
            planDraft.setProdYearMonth(draftDTO.getProdYearMonth());

            // 处理第一个时段数据
            if (!draftDTO.getPeriodReports().isEmpty()) {
                PeriodReportDTO firstPeriod = draftDTO.getPeriodReports().get(0);
                planDraft.setPeriodStartDate(firstPeriod.getPeriodStartDate());
                planDraft.setPeriodEndDate(firstPeriod.getPeriodEndDate());
                planDraft.setPeriodDays(firstPeriod.getPeriodDays());
                planDraft.setDailyGasVolume(firstPeriod.getDailyGasVolume());
            }

            planDraft.setRemarks(draftDTO.getRemarks());
            planDraft.setCreateTime(now);
            planDraft.setUpdateTime(now);
            planDraft.setBsflag(1);

            planDraftMapper.insert(planDraft);

            // 如果有多个时段，需要创建额外的草稿记录
            if (draftDTO.getPeriodReports().size() > 1) {
                for (int i = 1; i < draftDTO.getPeriodReports().size(); i++) {
                    PeriodReportDTO period = draftDTO.getPeriodReports().get(i);
                    PlanDraft additionalDraft = new PlanDraft();
                    BeanUtils.copyProperties(planDraft, additionalDraft);
                    additionalDraft.setDraftId(generateDraftId());
                    additionalDraft.setPeriodStartDate(period.getPeriodStartDate());
                    additionalDraft.setPeriodEndDate(period.getPeriodEndDate());
                    additionalDraft.setPeriodDays(period.getPeriodDays());
                    additionalDraft.setDailyGasVolume(period.getDailyGasVolume());

                    planDraftMapper.insert(additionalDraft);
                }
            }

            return new PlanDraftResultVO(planDraft.getDraftId(), now);
        } else {
            // 更新现有草稿
            // 先删除所有相关草稿
            LambdaQueryWrapper<PlanDraft> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PlanDraft::getTaskId, draftDTO.getTaskId())
                   .eq(PlanDraft::getTaskAssignId, draftDTO.getTaskAssignId())
                   .eq(PlanDraft::getEmployeeId, draftDTO.getEmployeeId());

            planDraftMapper.delete(wrapper);

            // 重新创建草稿
            String draftId = generateDraftId();
            for (PeriodReportDTO period : draftDTO.getPeriodReports()) {
                PlanDraft planDraft = new PlanDraft();
                planDraft.setDraftId(draftId);
                planDraft.setTaskId(draftDTO.getTaskId());
                planDraft.setTaskAssignId(draftDTO.getTaskAssignId());
                planDraft.setOrgId(draftDTO.getOrgId());
                planDraft.setEmployeeId(draftDTO.getEmployeeId());
                planDraft.setProdPeriod(draftDTO.getProdPeriod());
                planDraft.setProdYearMonth(draftDTO.getProdYearMonth());
                planDraft.setPeriodStartDate(period.getPeriodStartDate());
                planDraft.setPeriodEndDate(period.getPeriodEndDate());
                planDraft.setPeriodDays(period.getPeriodDays());
                planDraft.setDailyGasVolume(period.getDailyGasVolume());
                planDraft.setRemarks(draftDTO.getRemarks());
                planDraft.setCreateTime(now);
                planDraft.setUpdateTime(now);
                planDraft.setBsflag(1);

                planDraftMapper.insert(planDraft);

                // 只需要记录第一个时段的ID
                if (draftId.equals(planDraft.getDraftId())) {
                    draftId = planDraft.getDraftId();
                }
            }

            return new PlanDraftResultVO(draftId, now);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PlanSubmitResultVO submitPlan(PlanDraftDTO draftDTO) {
        // 1. 先保存草稿
        saveDraft(draftDTO);

        // 2. 将草稿数据转换为正式数据
        // TODO: 实现将按时段填报的数据转换为按日期存储的数据，保存到op_ugs_inj_pro_plan_month表中

        // 3. 更新任务状态为已完成
        TaskAssignment assignment = taskAssignmentMapper.selectById(draftDTO.getTaskAssignId());
        if (assignment != null) {
            assignment.setTaskStatus(TaskStatusEnum.COMPLETED.getCode());
            assignment.setFinishTime(LocalDateTime.now());
            assignment.setUpdateDate(LocalDateTime.now());
            assignment.setUpdateUserId(SecurityUtils.getCurrentUserId());

            taskAssignmentMapper.updateById(assignment);
        }

        // 4. 记录任务提交日志
        String currentUserId = SecurityUtils.getCurrentUserId();
        LocalDateTime now = LocalDateTime.now();

        TaskLog taskLog = new TaskLog();
        taskLog.setLogId(generateLogId());
        taskLog.setTaskId(draftDTO.getTaskId());
        taskLog.setTaskAssignId(draftDTO.getTaskAssignId());
        taskLog.setOperationType("提交");
        taskLog.setOperationTime(now);
        taskLog.setOperatorId(currentUserId);
        taskLog.setOperationDesc("提交填报内容");
        taskLog.setBsflag(1);
        taskLog.setCreateDate(now);
        taskLog.setCreateUserId(currentUserId);
        taskLog.setUpdateDate(now);
        taskLog.setUpdateUserId(currentUserId);

        taskLogMapper.insert(taskLog);

        return new PlanSubmitResultVO(draftDTO.getTaskId(), now);
    }

    @Override
    public PlanDraftVO getDraft(String taskId, String taskAssignId, String employeeId) {
        // 查询草稿信息
        LambdaQueryWrapper<PlanDraft> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlanDraft::getTaskId, taskId)
               .eq(PlanDraft::getTaskAssignId, taskAssignId)
               .eq(PlanDraft::getEmployeeId, employeeId)
               .eq(PlanDraft::getBsflag, 1);

        List<PlanDraft> drafts = planDraftMapper.selectList(wrapper);
        if (drafts.isEmpty()) {
            return null;
        }

        // 构建返回对象
        PlanDraftVO draftVO = new PlanDraftVO();
        PlanDraft firstDraft = drafts.get(0);

        draftVO.setDraftId(firstDraft.getDraftId());
        draftVO.setTaskId(firstDraft.getTaskId());
        draftVO.setTaskAssignId(firstDraft.getTaskAssignId());
        draftVO.setOrgId(firstDraft.getOrgId());
        draftVO.setEmployeeId(firstDraft.getEmployeeId());
        draftVO.setProdPeriod(firstDraft.getProdPeriod());
        draftVO.setProdYearMonth(firstDraft.getProdYearMonth());
        draftVO.setRemarks(firstDraft.getRemarks());
        draftVO.setCreateTime(firstDraft.getCreateTime());
        draftVO.setUpdateTime(firstDraft.getUpdateTime());

        // 构建时段数据
        List<PeriodReportDTO> periodReports = new ArrayList<>();
        for (PlanDraft draft : drafts) {
            PeriodReportDTO periodReport = new PeriodReportDTO();
            periodReport.setPeriodStartDate(draft.getPeriodStartDate());
            periodReport.setPeriodEndDate(draft.getPeriodEndDate());
            periodReport.setPeriodDays(draft.getPeriodDays());
            periodReport.setDailyGasVolume(draft.getDailyGasVolume());

            periodReports.add(periodReport);
        }

        draftVO.setPeriodReports(periodReports);

        return draftVO;
    }

    @Override
    public IPage<TaskVO> pageTask(TaskQueryDTO queryDTO) {
        Page<TaskVO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        IPage<TaskVO> result = baseTaskMapper.pageTask(page, queryDTO, SecurityUtils.getCurrentUserId());

        // 为每个任务加载分配信息
        for (TaskVO taskVO : result.getRecords()) {
            List<TaskAssignmentVO> assignments = taskAssignmentMapper.listByTaskId(taskVO.getTaskId());
            taskVO.setAssignments(assignments);
        }

        return result;
    }

    @Override
    public TaskDetailVO getTaskDetail(String taskId) {
        // 1. 查询任务基本信息
        BaseTask baseTask = baseTaskMapper.selectById(taskId);
        if (baseTask == null) {
            throw new RuntimeException("任务不存在");
        }

        // 2. 构建任务详情对象
        TaskDetailVO detailVO = new TaskDetailVO();
        BeanUtils.copyProperties(baseTask, detailVO);

        // 3. 查询任务分配信息
        List<TaskAssignmentVO> assignments = taskAssignmentMapper.listByTaskId(taskId);
        detailVO.setAssignments(assignments);

        // 4. 设置任务状态名称
        detailVO.setTaskStatusName(TaskStatusEnum.getNameByCode(baseTask.getTaskStatus()));

        // 5. 设置创建和更新用户信息
        detailVO.setCreateUser(baseTask.getCreateUserId());
        detailVO.setUpdateUser(baseTask.getUpdateUserId());
        detailVO.setCreateTime(baseTask.getCreateDate());
        detailVO.setUpdateTime(baseTask.getUpdateDate());

        return detailVO;
    }

    @Override
    public List<TaskLogVO> getTaskLogs(String taskId) {
        return taskLogMapper.listByTaskId(taskId);
    }

    @Override
    public PlanReportVO getPlanReport(String taskId, String orgId) {
        // TODO: 实现获取填报结果的逻辑
        return null;
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId() {
        return "TASK" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 4);
    }

    /**
     * 生成任务分配ID
     */
    private String generateTaskAssignId() {
        return "ASSIGN" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 4);
    }

    /**
     * 生成日志ID
     */
    private String generateLogId() {
        return "LOG" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 4);
    }

    /**
     * 生成草稿ID
     */
    private String generateDraftId() {
        return "DRAFT" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 4);
    }
}
