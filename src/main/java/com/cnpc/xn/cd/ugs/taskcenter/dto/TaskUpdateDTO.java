package com.cnpc.xn.cd.ugs.taskcenter.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 任务更新DTO
 */
@Data
public class TaskUpdateDTO {
    
    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String taskName;
    
    /**
     * 计划年月，格式：YYYY-MM
     */
    @NotBlank(message = "计划年月不能为空")
    private String planYearMonth;
    
    /**
     * 开始日期
     */
    @NotNull(message = "开始日期不能为空")
    private LocalDateTime startTime;
    
    /**
     * 结束日期
     */
    @NotNull(message = "结束日期不能为空")
    private LocalDateTime endTime;
    
    /**
     * 任务说明
     */
    private String taskDesc;
}
