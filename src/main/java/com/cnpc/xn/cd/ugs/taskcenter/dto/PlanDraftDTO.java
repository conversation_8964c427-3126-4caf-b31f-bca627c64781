package com.cnpc.xn.cd.ugs.taskcenter.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 计划草稿DTO
 */
@Data
public class PlanDraftDTO {
    
    /**
     * 任务ID
     */
    @NotBlank(message = "任务ID不能为空")
    private String taskId;
    
    /**
     * 任务分配ID
     */
    @NotBlank(message = "任务分配ID不能为空")
    private String taskAssignId;
    
    /**
     * 组织机构ID
     */
    @NotBlank(message = "组织机构ID不能为空")
    private String orgId;
    
    /**
     * 填报人员ID
     */
    @NotBlank(message = "填报人员ID不能为空")
    private String employeeId;
    
    /**
     * 生产阶段，PRO=采气，INJ=注气
     */
    @NotBlank(message = "生产阶段不能为空")
    private String prodPeriod;
    
    /**
     * 生产计划年月，格式：YYYY-MM
     */
    @NotBlank(message = "生产计划年月不能为空")
    private String prodYearMonth;
    
    /**
     * 时段填报数据
     */
    @NotEmpty(message = "时段填报数据不能为空")
    @Valid
    private List<PeriodReportDTO> periodReports;
    
    /**
     * 备注
     */
    private String remarks;
}
