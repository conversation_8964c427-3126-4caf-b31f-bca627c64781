package com.cnpc.xn.cd.ugs.taskcenter.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @ClassName IpWhiteCidrConfig
 * <p>
 * 访问ip段白名单配置
 * </p>
 * @date 2024/10/12 14:05
 **/
@Configuration
public class EncConfig {
    @Value("${kld_config_key:ZltZ9xX07cdGJTpc7N0Mpr6UJa4w/Sj7vHnuY4RJyTs=}")
    private String aesKey;

    public String getAesKey() {
        return aesKey;
    }

    public void setAesKey(String aesKey) {
        this.aesKey = aesKey;
    }
}
