package com.cnpc.xn.cd.ugs.taskcenter.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.IOUtils;
import org.apache.http.*;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.Map.Entry;

/***
 * 后台发送get或post请求
 *
 * <AUTHOR>
 *
 */
@SuppressWarnings("deprecation")
public class HttpClientUtil {

	private static final String DEFAULT_CHAR_SET = "UTF-8";
	public static final int DO_GET = 0;
	public static final int DO_POST = 1;
	public static final int DO_PATCH = 2;
	private static final Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);
	private static final String HTTPS_PROTOCOL = "https";

	public static final class NetworkProxy{
		public String address; //地址
		public int port; //端口
		public String protocol; //协议
	}

	/**
	 * 调用远程服务
	 *
	 * @param url         远程服务地址
	 * @param requestParams 请求参数
	 * @param methodType    请求方法类型
	 * @return 返回结果，Map类型，包含调用结果数据
	 */
	public static Map<String, Object> invoke(final String url,
		final Map<String, Object> requestParams,
		final int methodType){
		return invoke(url, requestParams, methodType);
	}

	/**
	 * 调用远程接口
	 *
	 * @param url           远程接口URL
	 * @param requestParams 请求参数
	 * @param methodType    请求方法类型，例如GET、POST等
	 * @param proxy         网络代理
	 * @return 远程接口返回的结果，键为返回字段名称，值为返回字段的值
	 * @throws Exception 如果请求失败，将抛出异常
	 */
	public static Map<String, Object> invoke(final String url,
			final Map<String, Object> requestParams,
			final int methodType,
			final NetworkProxy proxy){
		return invoke(url, requestParams, null, methodType, proxy);
	}
	/**
	 * 调用方法
	 *
	 * @param url  请求url
	 * @param requestParams 参数集合 Map<String,Object>
	 * @param headerParams 请求头信息
	 * @param methodType  调用方法类型 HttpClientUtil.DO_GET 或 0调用doGet,HttpClientUtil.DO_POST
	 *            或 1 调用doPost
	 * @return
	 */
	public static Map<String, Object> invoke(final String url,
		final Map<String, Object> requestParams,
		final Map<String, String> headerParams,
		final int methodType,
		final NetworkProxy proxy) {
		if (methodType == DO_GET) {
			return doGet(url, requestParams, headerParams, proxy);
		}
		if(methodType == DO_PATCH){
			return doPatch(url, requestParams, headerParams, proxy);
		}
		return doPost(url, requestParams, headerParams, proxy);
	}

	public static Map<String, Object> invoke(final String url,
		final Map<String, Object> requestParams,
		final Map<String, String> headerParams,
		final int methodType) {
		return invoke(url, requestParams, headerParams, methodType);
	}

	public static Map<String, Object> doPost(final String url,
		final Map<String, Object> requestParams){
		return doPost(url, requestParams, null, null);
	}

	public static Map<String, Object> doPost(final String url,
			final Map<String, Object> requestParams,
			final NetworkProxy proxy){
			return doPost(url, requestParams, null, proxy);
		}

	public static Map<String, Object> doPost(final String url,
		final Map<String, Object> requestParams,
		final Map<String, String> headerParams,
		final NetworkProxy proxy) {
		return request(url, DO_POST, requestParams, headerParams, proxy);
	}

	public static Map<String, Object> doPatch(final String url,
		 final Map<String, Object> requestParams){
		return doPatch(url, requestParams, null, null);
	}

	public static Map<String, Object> doPatch(final String url,
		 final Map<String, Object> requestParams,
		 final NetworkProxy proxy){
		return doPatch(url, requestParams, null, proxy);
	}

	public static Map<String, Object> doPatch(final String url,
		 final Map<String, Object> requestParams,
		 final Map<String, String> headerParams,
		 final NetworkProxy proxy) {
		return request(url, DO_PATCH, requestParams, headerParams, proxy);
	}

	private static Map<String, Object> request(final String url,
		 final int methodType,
		 final Map<String, Object> requestParams,
		 final Map<String, String> headerParams,
		 final NetworkProxy proxy) {
		final CloseableHttpClient httpClient = getHttpClient(url);
		CloseableHttpResponse httpResponse = null;
		Map<String, Object> resultMap = null;
		try {
			final HttpUriRequest request;
			if(methodType == DO_POST){
				request = new HttpPost(url);
			}else if(methodType == DO_PATCH){
				request = new HttpPatch(url);
			}else{
				String uri = url;
				if (CollectionUtils.isNotEmpty(requestParams)) {
					final StringBuilder sb = new StringBuilder();
					for (final Entry<String, Object> entry : requestParams.entrySet()) {
						final String value = entry.getValue() == null ? "" : URLEncoder.encode(entry.getValue().toString(), "UTF-8");
						uri = HttpUtils.addUrlParameter(uri, entry.getKey(), value);
					}
				}
				request = new HttpGet(uri);
			}
			if(proxy != null){
				final HttpHost proxyHost = new HttpHost(proxy.address, proxy.port, proxy.protocol);
				final RequestConfig config = RequestConfig.custom().setProxy(proxyHost).build();
				((HttpRequestBase)request).setConfig(config);
			}
			if(CollectionUtils.isNotEmpty(headerParams)){
				final Iterator<Entry<String, String>> iterator = headerParams.entrySet().iterator();
				while (iterator.hasNext()) {
					final Entry<String, String> entry = (Entry<String, String>) iterator.next();
					request.addHeader(entry.getKey(), entry.getValue());
				}
			}

			// 设置参数
			if(methodType != DO_GET && CollectionUtils.isNotEmpty(requestParams)){
				final HttpEntityEnclosingRequestBase httpEntityRequest = (HttpEntityEnclosingRequestBase) request;
				if(headerParams != null
						&& headerParams.containsKey("Content-Type")
						&& "application/json".equalsIgnoreCase(headerParams.get("Content-Type"))) {
					final StringEntity se = new StringEntity(JSON.toJSONString(requestParams), null, "UTF-8");
					httpEntityRequest.setEntity(se);
				}else
				{
					final List<NameValuePair> list = new ArrayList<>();
					final Iterator<Entry<String, Object>> iterator = requestParams.entrySet().iterator();
					while (iterator.hasNext()) {
						final Entry<String, Object> entry = (Entry<String, Object>) iterator.next();
						list.add(new BasicNameValuePair(entry.getKey(), entry.getValue() == null ? null : entry.getValue().toString()));
					}
					if (list.size() > 0) {
						final UrlEncodedFormEntity entity = new UrlEncodedFormEntity(list, DEFAULT_CHAR_SET);
						httpEntityRequest.setEntity(entity);
					}
				}
			}
			httpResponse = httpClient.execute(request);
			if (httpResponse != null){
				final HttpEntity resEntity = httpResponse.getEntity();
				if (resEntity != null) {
					final String result = EntityUtils.toString(resEntity, DEFAULT_CHAR_SET);
					resultMap = parseJson(result);
				}
			}
		} catch (Exception ex) {
			logger.error("error : {}",ex);
			throw new HttpRequestException("数据请求错误：" + ex.getMessage(), ex);
		} finally {
			try {
				closeHttpResponse(httpResponse);
				closeHttpClient(httpClient);
			} catch (IOException e) {
				logger.error("error : {}",e);
			}
		}
		return resultMap;
	}

	private static Map<String, Object> parseJson(final String jsonStr){
		final JSONObject fastJson = JSON.parseObject(jsonStr);
		return (Map<String, Object>) fastJson;
	}

	public static Map<String, Object> doGet(final String url,
			final Map<String, Object> requestParams){
		return doGet(url, requestParams, null, null);
	}

	public static Map<String, Object> doGet(final String url,
			final Map<String, Object> requestParams,
			final Map<String, String> headerParams){
		return doGet(url, requestParams, headerParams, null);
	}

	public static Map<String, Object> doGet(final String url,
			final Map<String, Object> requestParams,
			final NetworkProxy proxy){
		return doGet(url, requestParams, null, proxy);
	}

	public static Map<String, Object> doGet(final String url,
			final Map<String, Object> requestParams,
			final Map<String, String> headerParams,
			final NetworkProxy proxy) {
		return request(url, DO_GET, requestParams, headerParams, proxy);
	}

	private static String getFileName(final HttpResponse response) {
        final Header contentHeader = response.getFirstHeader("Content-Disposition");
        String filename = null;
        if (contentHeader != null) {
            final HeaderElement[] values = contentHeader.getElements();
            if (values.length == 1) {
                final NameValuePair param = values[0].getParameterByName("filename");
                if (param != null) {
                    filename = param.getValue();
                }
            }
        }
        return filename;
    }

	/**
	 * 通过http请求获取文件
	 * @param url
	 * @param outDir
	 * @return
	 */
	public static File downloadFile(final String url, final File outDir){
		final CloseableHttpClient client = getHttpClient(url);
		CloseableHttpResponse httpResponse = null;
		// 用get方法发送http请求
		final HttpGet get = new HttpGet(url);
		// 发送get请求
		try {
			httpResponse = client.execute(get);
		} catch (ClientProtocolException e) {
			logger.error("error : {}",e);
			return null;
		} catch (IOException e) {
			logger.error("error : {}",e);
			return null;
		}
		final String fileName = getFileName(httpResponse);

        final HttpEntity entity = httpResponse.getEntity();
        InputStream is = null;
        final File file = new File(outDir, fileName);
        FileOutputStream fileout = null;
        final byte[] buffer=new byte[1024 * 512];
        try {
        	is = entity.getContent();
        	fileout = new FileOutputStream(file);
        	int read = -1;
			while ((read = is.read(buffer)) != -1) {
			    fileout.write(buffer, 0, read);
			}
		} catch (IOException e) {
			logger.error("error : {}",e);
			return null;
		}  finally{
			IOUtils.closeQuietly(is);
			IOUtils.closeQuietly(fileout);
			try {
				httpResponse.close();
			} catch (IOException e) {
				logger.error("error : {}",e);
			}
		}
        return file;
	}

	/**
	 * 获取httpClient
	 *
	 * @param url
	 * @return
	 */
	private static CloseableHttpClient getHttpClient(final String url) {
		if (url.startsWith(HTTPS_PROTOCOL)) {
			return new SslClient();
		}
		return HttpClients.createDefault();
	}

	/**
	 * 关闭httpClient
	 *
	 * @param client
	 * @throws IOException
	 */
	private static void closeHttpClient(final CloseableHttpClient client)
			throws IOException {
		if (client != null) {
			client.close();
		}
	}

	/**
	 * 关闭httpResponse
	 *
	 * @param httpResponse
	 * @throws IOException
	 */
	private static void closeHttpResponse(
			final CloseableHttpResponse httpResponse) throws IOException {
		if (httpResponse != null) {
			httpResponse.close();
		}
	}

	public static void main(String[] args) {
		String url = "http://11.10.65.211//jsvc/service/nss/SE_JZ_JWH_ZSJ.json";
		// String url
		// String url = "http://11.10.65.211/jsvc/service/sys_call_services/excuteSql.json"
		// + "?sqlCode=EE_KT_KANTAN_TARGET&queryMap.weekNo=201550&queryMap.weekOrMonth=周";
		Map<String, Object> params = new HashMap<>(16);
		params.put("yearWeek", "201808");
		Map<String, Object> resultMap = null;
		resultMap = HttpClientUtil.invoke(url, params, HttpClientUtil.DO_POST);
		System.out.println(resultMap);
		System.out.println((Boolean) resultMap.get("success"));
	}
}

