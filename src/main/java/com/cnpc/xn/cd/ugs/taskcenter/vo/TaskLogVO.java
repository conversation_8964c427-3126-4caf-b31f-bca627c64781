package com.cnpc.xn.cd.ugs.taskcenter.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务日志视图对象
 */
@Data
public class TaskLogVO {
    
    /**
     * 日志ID
     */
    private String logId;
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务分配ID
     */
    private String taskAssignId;
    
    /**
     * 操作类型
     */
    private String operationType;
    
    /**
     * 操作时间
     */
    private LocalDateTime operationTime;
    
    /**
     * 操作人ID
     */
    private String operatorId;
    
    /**
     * 操作人姓名
     */
    private String operatorName;
    
    /**
     * 操作描述
     */
    private String operationDesc;
    
    /**
     * 耗时(小时)
     */
    private Integer duration;
}
