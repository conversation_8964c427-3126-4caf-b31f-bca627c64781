package com.cnpc.xn.cd.ugs.taskcenter.utils;

/**
 * <AUTHOR>
 * @ClassName InflectionPointFinder
 * <p>
 *
 * </p>
 * @date 2024/11/06 18:37
 **/
import java.util.ArrayList;
import java.util.List;



public class InflectionPointFinderUtilSecond {

    // 计算两点之间的斜率
    private static double calculateSlope(Point p1, Point p2) {
        if (p1.x == p2.x) {
            throw new IllegalArgumentException("Cannot calculate slope for vertical lines.");
        }
        return (p2.y - p1.y) / (p2.x - p1.x);
    }

    // 寻找拐点
    public static List<Point> findInflectionPoints(List<Point> points, double slopeThreshold) {
        List<Point> inflectionPoints = new ArrayList<>();

        for (int i = 1; i < points.size() - 1; i++) {
            Point pPrev = points.get(i - 1);
            Point pCurrent = points.get(i);
            Point pNext = points.get(i + 1);

            double slopePrev = calculateSlope(pPrev, pCurrent);
            double slopeNext = calculateSlope(pCurrent, pNext);

            // 计算斜率的变化量
            double slopeDifference = Math.abs(slopePrev - slopeNext);

            // 如果斜率变化量超过了阈值，则将当前点视为拐点
            if (slopeDifference > slopeThreshold) {
                inflectionPoints.add(pCurrent);
            }
        }

        return inflectionPoints;
    }

    public static void main(String[] args) {
        // 示例点集
        List<Point> points = new ArrayList<>();
        points.add(new Point(1, 1));
        points.add(new Point(2, 4));
        points.add(new Point(3, 9));
        points.add(new Point(4, 16));
        points.add(new Point(5, 20)); // 这里假设是一个拐点，因为之后斜率会大幅下降
        points.add(new Point(6, 21));
        points.add(new Point(7, 24));

        // 设置斜率变化的阈值
        double slopeThreshold = 2.0;

        // 寻找拐点
        List<Point> inflectionPoints = findInflectionPoints(points, slopeThreshold);

        // 输出拐点
        System.out.println("Inflection Points:");
        for (Point point : inflectionPoints) {
            System.out.println("(" + point.x + ", " + point.y + ")");
        }
    }
}