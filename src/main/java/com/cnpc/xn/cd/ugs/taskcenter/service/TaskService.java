package com.cnpc.xn.cd.ugs.taskcenter.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cnpc.xn.cd.ugs.taskcenter.dto.*;
import com.cnpc.xn.cd.ugs.taskcenter.vo.*;

import java.util.List;

/**
 * 任务管理服务接口
 */
public interface TaskService {
    
    /**
     * 创建任务
     *
     * @param createDTO 任务创建DTO
     * @return 任务ID和创建时间
     */
    TaskCreateResultVO createTask(TaskCreateDTO createDTO);
    
    /**
     * 修改任务
     *
     * @param taskId 任务ID
     * @param updateDTO 任务更新DTO
     * @return 任务ID和更新时间
     */
    TaskUpdateResultVO updateTask(String taskId, TaskUpdateDTO updateDTO);
    
    /**
     * 删除任务
     *
     * @param taskId 任务ID
     */
    void deleteTask(String taskId);
    
    /**
     * 指派任务
     *
     * @param assignDTO 任务指派DTO
     * @return 任务分配ID和指派时间
     */
    TaskAssignResultVO assignTask(TaskAssignDTO assignDTO);
    
    /**
     * 调整任务指派
     *
     * @param taskAssignId 任务分配ID
     * @param reassignDTO 任务重新指派DTO
     * @return 任务分配ID和重新指派时间
     */
    TaskReassignResultVO reassignTask(String taskAssignId, TaskReassignDTO reassignDTO);
    
    /**
     * 保存草稿
     *
     * @param draftDTO 计划草稿DTO
     * @return 草稿ID和保存时间
     */
    PlanDraftResultVO saveDraft(PlanDraftDTO draftDTO);
    
    /**
     * 提交填报
     *
     * @param draftDTO 计划草稿DTO
     * @return 任务ID和提交时间
     */
    PlanSubmitResultVO submitPlan(PlanDraftDTO draftDTO);
    
    /**
     * 获取草稿
     *
     * @param taskId 任务ID
     * @param taskAssignId 任务分配ID
     * @param employeeId 填报人员ID
     * @return 草稿信息
     */
    PlanDraftVO getDraft(String taskId, String taskAssignId, String employeeId);
    
    /**
     * 分页查询任务列表
     *
     * @param queryDTO 查询条件
     * @return 任务列表
     */
    IPage<TaskVO> pageTask(TaskQueryDTO queryDTO);
    
    /**
     * 获取任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    TaskDetailVO getTaskDetail(String taskId);
    
    /**
     * 获取任务日志
     *
     * @param taskId 任务ID
     * @return 任务日志列表
     */
    List<TaskLogVO> getTaskLogs(String taskId);
    
    /**
     * 获取填报结果
     *
     * @param taskId 任务ID
     * @param orgId 组织机构ID
     * @return 填报结果
     */
    PlanReportVO getPlanReport(String taskId, String orgId);
}
