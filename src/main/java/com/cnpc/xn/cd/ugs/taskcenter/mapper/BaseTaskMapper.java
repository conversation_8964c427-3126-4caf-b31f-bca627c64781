package com.cnpc.xn.cd.ugs.taskcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cnpc.xn.cd.ugs.taskcenter.dto.TaskQueryDTO;
import com.cnpc.xn.cd.ugs.taskcenter.entity.BaseTask;
import com.cnpc.xn.cd.ugs.taskcenter.vo.TaskVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 任务基本信息Mapper接口
 */
@Mapper
public interface BaseTaskMapper extends BaseMapper<BaseTask> {
    
    /**
     * 分页查询任务列表
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @param userId 当前用户ID
     * @return 任务列表
     */
    IPage<TaskVO> pageTask(Page<TaskVO> page, @Param("query") TaskQueryDTO queryDTO, @Param("userId") String userId);
}
