package com.cnpc.xn.cd.ugs.taskcenter.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 时段填报DTO
 */
@Data
public class PeriodReportDTO {
    
    /**
     * 时段开始时间
     */
    @NotNull(message = "时段开始时间不能为空")
    private LocalDate periodStartDate;
    
    /**
     * 时段结束时间
     */
    @NotNull(message = "时段结束时间不能为空")
    private LocalDate periodEndDate;
    
    /**
     * 时段天数
     */
    @NotNull(message = "时段天数不能为空")
    @Positive(message = "时段天数必须大于0")
    private Integer periodDays;
    
    /**
     * 日采气/注气量(万方)
     */
    @NotNull(message = "日采气/注气量不能为空")
    @Positive(message = "日采气/注气量必须大于0")
    private BigDecimal dailyGasVolume;
}
