package com.cnpc.xn.cd.ugs.taskcenter.utils;

import java.io.Serializable;

/**
 * API返回结果包装类
 *
 * <AUTHOR>
 * @date 2024/11/07
 */
public class ApiResult implements Serializable {
    private String code;
    private String msg;
    private boolean flag = true;
    private Object result;
    private String jumpUrl;

    private ApiResult() {
    }

    public static ApiResult newInstance() {
        return new ApiResult();
    }

    public static ApiResult ofSuccessResultMsg(Object result, String msg, Object... args) {
        ApiResult apiResult = newInstance();
        apiResult.setFlag(true);
        apiResult.setResult(result);
        apiResult.setMsg(String.format(msg, args));
        return apiResult;
    }

    public static ApiResult ofSuccessResult(Object result) {
        return ofSuccessResultMsg(result, "操作成功！");
    }

    public static ApiResult ofSuccess() {
        return ofSuccessResult((Object)null);
    }

    public static ApiResult ofFailureResultMsg(Object result, String msg, Object... args) {
        ApiResult apiResult = newInstance();
        apiResult.setFlag(false);
        apiResult.setResult(result);
        apiResult.setMsg(String.format(msg, args));
        return apiResult;
    }

    public static ApiResult ofFailureResult(Object result) {
        return ofFailureResultMsg(result, "操作失败！");
    }

    public static ApiResult ofFailure() {
        return ofFailureResultMsg((Object)null, "操作失败！");
    }

    public String getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }

    public boolean isFlag() {
        return this.flag;
    }

    public Object getResult() {
        return this.result;
    }

    public String getJumpUrl() {
        return this.jumpUrl;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public void setFlag(boolean flag) {
        this.flag = flag;
    }

    public void setResult(Object result) {
        this.result = result;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof ApiResult)) {
            return false;
        } else {
            ApiResult other = (ApiResult)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label63: {
                    Object thisCode = this.getCode();
                    Object otherCode = other.getCode();
                    if (thisCode == null) {
                        if (otherCode == null) {
                            break label63;
                        }
                    } else if (thisCode.equals(otherCode)) {
                        break label63;
                    }

                    return false;
                }

                Object thisMsg = this.getMsg();
                Object otherMsg = other.getMsg();
                if (thisMsg == null) {
                    if (otherMsg != null) {
                        return false;
                    }
                } else if (!thisMsg.equals(otherMsg)) {
                    return false;
                }

                if (this.isFlag() != other.isFlag()) {
                    return false;
                } else {
                    Object thisResult = this.getResult();
                    Object otherResult = other.getResult();
                    if (thisResult == null) {
                        if (otherResult != null) {
                            return false;
                        }
                    } else if (!thisResult.equals(otherResult)) {
                        return false;
                    }

                    Object thisJumpUrl = this.getJumpUrl();
                    Object otherJumpUrl = other.getJumpUrl();
                    if (thisJumpUrl == null) {
                        if (otherJumpUrl != null) {
                            return false;
                        }
                    } else if (!thisJumpUrl.equals(otherJumpUrl)) {
                        return false;
                    }

                    return true;
                }
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof ApiResult;
    }

    public int hashCode() {
        boolean PRIME = true;
        int result = 1;
        Object $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        Object $msg = this.getMsg();
        result = result * 59 + ($msg == null ? 43 : $msg.hashCode());
        result = result * 59 + (this.isFlag() ? 79 : 97);
        Object $result = this.getResult();
        result = result * 59 + ($result == null ? 43 : $result.hashCode());
        Object $jumpUrl = this.getJumpUrl();
        result = result * 59 + ($jumpUrl == null ? 43 : $jumpUrl.hashCode());
        return result;
    }

    public String toString() {
        return "ApiResult(code=" + this.getCode() + ", msg=" + this.getMsg() + ", flag=" + this.isFlag() + ", result=" + this.getResult() + ", jumpUrl=" + this.getJumpUrl() + ")";
    }
}
