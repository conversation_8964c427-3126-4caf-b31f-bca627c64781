package com.cnpc.xn.cd.ugs.taskcenter.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName IpWhiteCidrConfig
 * <p>
 * 访问ip段白名单配置
 * </p>
 * @date 2024/10/12 14:05
 **/
@Configuration
@ConfigurationProperties(prefix = "ip.white.cidr.whites")
public class IpWhitesConfig {
    private List<String> whites = new ArrayList<>();

    public List<String> getWhites() {
        return whites;
    }

    public void setWhites(List<String> whites) {
        this.whites = whites;
    }
}
