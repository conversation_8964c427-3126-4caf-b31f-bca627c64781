package com.cnpc.xn.cd.ugs.taskcenter.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 储气库生产运行计划草稿表实体类
 */
@Data
@TableName("dawfl.op_ugs_inj_pro_plan_draft")
public class PlanDraft {
    
    /**
     * 草稿ID
     */
    @TableId(value = "draft_id", type = IdType.INPUT)
    private String draftId;
    
    /**
     * 任务ID
     */
    @TableField("task_id")
    private String taskId;
    
    /**
     * 任务分配ID
     */
    @TableField("task_assign_id")
    private String taskAssignId;
    
    /**
     * 组织机构ID
     */
    @TableField("org_id")
    private String orgId;
    
    /**
     * 填报人员ID
     */
    @TableField("employee_id")
    private String employeeId;
    
    /**
     * 生产阶段
     */
    @TableField("prod_period")
    private String prodPeriod;
    
    /**
     * 生产计划年月
     */
    @TableField("prod_year_month")
    private String prodYearMonth;
    
    /**
     * 时段开始时间
     */
    @TableField("period_start_date")
    private LocalDate periodStartDate;
    
    /**
     * 时段结束时间
     */
    @TableField("period_end_date")
    private LocalDate periodEndDate;
    
    /**
     * 时段天数
     */
    @TableField("period_days")
    private Integer periodDays;
    
    /**
     * 日采气/注气量
     */
    @TableField("daily_gas_volume")
    private BigDecimal dailyGasVolume;
    
    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
    
    /**
     * 数据逻辑删除标识，1=在用，-5=废弃
     */
    @TableField("bsflag")
    private Integer bsflag;
}
