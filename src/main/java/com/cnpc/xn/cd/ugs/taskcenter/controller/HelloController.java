package com.cnpc.xn.cd.ugs.taskcenter.controller;

import cnpc.udp.framework.basic.common.crypto.symmetric.AesUtil;
import cnpc.udp.framework.redis.core.UdpRedisService;
import com.alibaba.fastjson.JSON;
import com.cnpc.xn.cd.ugs.taskcenter.config.EncConfig;
import com.cnpc.xn.cd.ugs.taskcenter.service.impl.HelloService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@CrossOrigin(origins = "*")
public class HelloController {



    @Autowired
    private UdpRedisService kldRedisService;
    @Autowired
    private HelloService helloService;
    @Autowired
    private EncConfig encConfig;


    /**
     * 处理GET请求，请求路径为"hi"的方法
     *
     * @return 返回字符串"hello world!"
     */

    @GetMapping("/hi")
    public String hello(@RequestParam(value = "name", required = false, defaultValue = "test") String name) {
        RedissonClient redissonClient = kldRedisService.getRedissonClient();
        RBucket<String> bucket = redissonClient.getBucket(name);
        String result = bucket.get();
        if (StringUtils.isEmpty(result)) {
            log.info("未命中缓存：");
            Map<String, String> map = new HashMap<>();
            map.put("hello", JSON.toJSONString(helloService.helloPg()));
            map.put("id", name);
            result = JSON.toJSONString(map);
//            bucket.set(JSON.toJSONString(map));
            bucket.set(result, Duration.ofSeconds(10));
        } else {
            log.info("命中缓存：{}" , bucket.get());
        }
        return result;
    }

    public static void main(String[] args) {
        try {
            System.out.println("KldCipher{"+ AesUtil.encrypt("n#LgGnf64jh#54!mj", "ZltZ9xX07cdGJTpc7N0Mpr6UJa4w/Sj7vHnuY4RJyTs=")+"}");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 对传入的字符串进行加密
     *
     * @param value 需要加密的字符串，如果不传值则默认为"test"
     * @return 加密后的字符串
     */
    @GetMapping("/config/enc")
    public String configEnc(@RequestParam(value = "value", required = false, defaultValue = "test") String value) {
        try {
            String encValue =  "KldCipher{"+AesUtil.encrypt(value, encConfig.getAesKey())+"}";
            log.info("value:{}", value);
            log.info("key:{}",encConfig.getAesKey());
            log.info("encValue:{}", encValue);
            return  encValue;
        } catch (Exception e) {
            log.error("configEnc.error value:{}; errorMsg:{}", value, e.getMessage());
            return "error:" + e.getMessage();
        }

    }

    /**
     * 处理解密请求的控制器方法
     * @param key 用于解密的密钥，如果未提供则默认为 "test"
     * @param value 需要解密的字符串值，如果未提供则默认为 "test"
     * @return 解密后的字符串，如果解密过程中出现异常，则返回错误信息
     */
    @GetMapping("/config/dec")
    public String configDec(@RequestParam(value = "key", required = false, defaultValue = "test") String key, @RequestParam(value = "value", required = false, defaultValue = "test") String value) {
        try {
            return AesUtil.decrypt(value,key);
        } catch (Exception e) {
            log.error("configDec.error value:{}; errorMsg:{}", value, e.getMessage());
            return "error:" + e.getMessage();
        }

    }




}
