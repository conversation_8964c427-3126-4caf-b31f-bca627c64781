package com.cnpc.xn.cd.ugs.taskcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cnpc.xn.cd.ugs.taskcenter.entity.TaskAssignment;
import com.cnpc.xn.cd.ugs.taskcenter.vo.TaskAssignmentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 任务分配Mapper接口
 */
@Mapper
public interface TaskAssignmentMapper extends BaseMapper<TaskAssignment> {
    
    /**
     * 根据任务ID查询任务分配信息
     *
     * @param taskId 任务ID
     * @return 任务分配信息列表
     */
    List<TaskAssignmentVO> listByTaskId(@Param("taskId") String taskId);
}
