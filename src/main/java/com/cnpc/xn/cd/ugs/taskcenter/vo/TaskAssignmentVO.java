package com.cnpc.xn.cd.ugs.taskcenter.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务分配视图对象
 */
@Data
public class TaskAssignmentVO {

    /**
     * 任务分配ID
     */
    private String taskAssignId;

    /**
     * 组织机构ID
     */
    private String orgId;

    /**
     * 组织机构名称
     */
    private String orgName;

    /**
     * 负责人ID
     */
    private String managerId;

    /**
     * 负责人姓名
     */
    private String managerName;

    /**
     * 执行人ID
     */
    private String employeeId;

    /**
     * 执行人姓名
     */
    private String employeeName;

    /**
     * 任务状态
     */
    private String taskStatus;

    /**
     * 任务状态名称
     */
    private String taskStatusName;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;
}
