package com.cnpc.xn.cd;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;


@SpringBootApplication(scanBasePackages ={ "cnpc.udp","com.cnpc"})
@ComponentScan(basePackages = {"cnpc.udp","com.cnpc"})
public class UgsTaskcenterApplication {

    public static void main(String[] args) {
        SpringApplication.run(UgsTaskcenterApplication.class, args);
        System.out.println("UgsTaskcenterApplication-----启动完毕...");
    }

}
