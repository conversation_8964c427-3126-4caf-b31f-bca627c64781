server:
  port: 7093

spring:
  application:
    name: ugs-taskcenter
  profiles:
    active: dev
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
  config:
    import: optional:nacos:ugs-taskcenter-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}?refreshEnabled=true
  cloud:
    nacos:
      username: ${nacos_username}
      password: ${nacos_password}
      config:
        # 配置中心地址
        server-addr: ${nacos_host}:${nacos_port}
        #       # 命名空间
        namespace: ${nacos_namespace}
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
        group: DEFAULT_GROUP

udp:
  mybatis:
    mapper-scan: com.cnpc.xn.cd.ugs.taskcenter
    config:
      validate: false
  redis:
    enable: false
  discovery:
    server-addr: ${nacos_host}:${nacos_port}
    service-name: yyy-fpfx
    discovery-type: nacos
    nacos:
      username: ${nacos_username}
      password: ${nacos_password}
      group: DEFAULT_GROUP
      namespace: ${nacos_namespace}
