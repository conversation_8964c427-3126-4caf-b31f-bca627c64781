<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnpc.xn.cd.ugs.taskcenter.mapper.PlanDraftMapper">

    <!-- 根据任务ID和填报人员ID查询草稿 -->
    <select id="getByTaskAndEmployee" resultType="com.cnpc.xn.cd.ugs.taskcenter.entity.PlanDraft">
        SELECT
            *
        FROM
            dawfl.op_ugs_inj_pro_plan_draft
        WHERE
            task_id = #{taskId}
            AND task_assign_id = #{taskAssignId}
            AND employee_id = #{employeeId}
            AND bsflag = 1
        ORDER BY
            create_time DESC
        LIMIT 1
    </select>

</mapper>
