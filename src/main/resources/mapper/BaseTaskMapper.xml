<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnpc.xn.cd.ugs.taskcenter.mapper.BaseTaskMapper">

    <!-- 分页查询任务列表 -->
    <select id="pageTask" resultType="com.cnpc.xn.cd.ugs.taskcenter.vo.TaskVO">
        SELECT DISTINCT
            t.task_id,
            t.task_name,
            t.sub_task_class,
            t.remarks AS plan_year_month,
            t.start_time,
            t.end_time,
            t.org_id,
            COALESCE(org.org_name, '') AS org_name,
            t.task_status,
            CASE t.task_status
                WHEN '1' THEN '已完成'
                WHEN '2' THEN '进行中'
                WHEN '3' THEN '已超期'
                ELSE ''
            END AS task_status_name,
            t.create_date AS create_time
        FROM
            dawfl.pm_base_task t
        LEFT JOIN
            dawfl.cd_organization org ON t.org_id = org.org_id AND org.bsflag = 1
        <if test="query.roleType != null and query.roleType != 'admin'">
        LEFT JOIN
            dawfl.pm_task_assignment a ON t.task_id = a.task_id AND a.bsflag = 1
        </if>
        <where>
            t.bsflag = 1
            <if test="query.roleType != null and query.roleType != 'admin'">
                AND (a.employee_id = #{userId} OR a.manager_id = #{userId})
            </if>
            <if test="query.taskStatus != null and query.taskStatus != ''">
                AND EXISTS (
                    SELECT 1 FROM dawfl.pm_task_assignment ta
                    WHERE ta.task_id = t.task_id
                    AND ta.bsflag = 1
                    AND ta.task_status = #{query.taskStatus}
                )
            </if>
            <if test="query.orgId != null and query.orgId != ''">
                AND t.org_id = #{query.orgId}
            </if>
            <if test="query.subTaskClass != null and query.subTaskClass != ''">
                AND t.sub_task_class = #{query.subTaskClass}
            </if>
            <if test="query.planYearMonth != null and query.planYearMonth != ''">
                AND t.remarks = #{query.planYearMonth}
            </if>
            <if test="query.startTime != null">
                AND t.start_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                AND t.end_time &lt;= #{query.endTime}
            </if>
        </where>
        ORDER BY t.create_date DESC
    </select>

</mapper>
