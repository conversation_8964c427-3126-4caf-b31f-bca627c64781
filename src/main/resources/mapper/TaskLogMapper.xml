<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnpc.xn.cd.ugs.taskcenter.mapper.TaskLogMapper">

    <!-- 根据任务ID查询任务日志 -->
    <select id="listByTaskId" resultType="com.cnpc.xn.cd.ugs.taskcenter.vo.TaskLogVO">
        SELECT
            l.log_id,
            l.task_id,
            l.task_assign_id,
            l.operation_type,
            l.operation_time,
            l.operator_id,
            COALESCE(u.display_name, '') AS operator_name,
            l.operation_desc,
            0 AS duration
        FROM
            dawfl.pm_task_log l
        LEFT JOIN
            dawfl.sys_user u ON l.operator_id = u.user_id AND u.bsflag = 1
        WHERE
            l.task_id = #{taskId}
            AND l.bsflag = 1
        ORDER BY
            l.operation_time ASC
    </select>

</mapper>
