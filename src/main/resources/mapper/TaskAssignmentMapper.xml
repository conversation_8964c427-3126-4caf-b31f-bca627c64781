<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnpc.xn.cd.ugs.taskcenter.mapper.TaskAssignmentMapper">

    <!-- 根据任务ID查询任务分配信息 -->
    <select id="listByTaskId" resultType="com.cnpc.xn.cd.ugs.taskcenter.vo.TaskAssignmentVO">
        SELECT
            a.task_assign_id,
            a.task_id,
            a.org_id,
            '' AS org_name,
            a.manager_id,
            '' AS manager_name,
            a.employee_id,
            '' AS employee_name,
            a.task_status,
            CASE a.task_status
                WHEN '1' THEN '已完成'
                WHEN '2' THEN '进行中'
                WHEN '3' THEN '已超期'
                ELSE ''
            END AS task_status_name,
            a.finish_time
        FROM
            dawfl.pm_task_assignment a
        WHERE
            a.task_id = #{taskId}
            AND a.bsflag = 1
        ORDER BY
            a.create_date ASC
    </select>

</mapper>
